import discord
from discord.ext import commands
from discord import app_commands
import aiohttp
import asyncio
import os
from typing import Dict, List, Optional, Any
import json
from datetime import datetime, timedelta
import logging

class UEXAPIClient:
    """Client for interacting with UEX API v2.0"""
    
    def __init__(self, api_token: str):
        self.api_token = api_token
        self.base_url = "https://api.uexcorp.space/2.0"
        self.session = None
        self.rate_limit_reset = None
        self.requests_remaining = 14400  # Daily quota
        
    async def _get_session(self):
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            headers = {
                'Authorization': f'Bearer {self.api_token}',
                'User-Agent': 'AegisNoxBot/1.0',
                'Accept': 'application/json'
            }
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """Make API request with rate limiting and error handling"""
        session = await self._get_session()
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with session.get(url, params=params) as response:
                # Update rate limit info
                self.requests_remaining = int(response.headers.get('X-RateLimit-Remaining', self.requests_remaining))
                
                if response.status == 429:
                    # Rate limited
                    retry_after = int(response.headers.get('Retry-After', 60))
                    logging.warning(f"UEX API rate limited. Retry after {retry_after} seconds")
                    await asyncio.sleep(retry_after)
                    return await self._make_request(endpoint, params)
                
                response.raise_for_status()
                data = await response.json()
                
                if data.get('status') != 'ok':
                    raise Exception(f"UEX API Error: {data.get('message', 'Unknown error')}")
                
                return data.get('data', [])
                
        except aiohttp.ClientError as e:
            logging.error(f"UEX API request failed: {e}")
            raise Exception(f"Failed to connect to UEX API: {e}")
    
    async def get_commodities(self) -> List[Dict]:
        """Get all commodities"""
        return await self._make_request('commodities')
    
    async def get_commodity_routes(self, **filters) -> List[Dict]:
        """Get commodity routes with optional filters"""
        # Routes API requires at least one parameter, so if none provided, get all commodities first
        if not filters:
            # Get a popular commodity to show default routes
            commodities = await self.get_commodities()
            if commodities:
                # Find a popular tradeable commodity
                popular_commodity = None
                for commodity in commodities:
                    if (commodity.get('is_available') and commodity.get('is_buyable') and
                        commodity.get('is_sellable') and commodity.get('price_sell', 0) > commodity.get('price_buy', 0)):
                        popular_commodity = commodity
                        break

                if popular_commodity:
                    filters = {'id_commodity': popular_commodity['id']}

        if not filters:
            return []  # Return empty if no valid filters

        return await self._make_request('commodities_routes', params=filters)
    
    async def get_commodity_prices(self, commodity_id: int = None) -> List[Dict]:
        """Get commodity prices"""
        endpoint = 'commodities_prices_all' if commodity_id is None else 'commodities_prices'
        params = {'id_commodity': commodity_id} if commodity_id else None
        return await self._make_request(endpoint, params=params)
    
    async def get_terminals(self) -> List[Dict]:
        """Get all terminals"""
        return await self._make_request('terminals')
    
    async def get_vehicles(self) -> List[Dict]:
        """Get all vehicles"""
        return await self._make_request('vehicles')
    
    async def close(self):
        """Close the session"""
        if self.session and not self.session.closed:
            await self.session.close()

class UEXTrade(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.uex_client = UEXAPIClient(os.getenv('UEX_API_TOKEN'))
        self.commodities_cache = {}
        self.vehicles_cache = {}
        self.terminals_cache = {}
        self.commodity_prices_cache = {}
        self.cache_expiry = None
        
    async def cog_load(self):
        """Initialize cache when cog loads"""
        await self._refresh_cache()
        
    async def cog_unload(self):
        """Clean up when cog unloads"""
        await self.uex_client.close()
    
    async def _refresh_cache(self):
        """Refresh cached data from UEX API"""
        try:
            # Cache commodities, vehicles, and terminals for autocomplete and lookups
            commodities = await self.uex_client.get_commodities()
            self.commodities_cache = {c['id']: c for c in commodities}

            vehicles = await self.uex_client.get_vehicles()
            self.vehicles_cache = {v['id']: v for v in vehicles}

            terminals = await self.uex_client.get_terminals()
            self.terminals_cache = {t['id']: t for t in terminals}

            # Cache commodity prices for trade route calculations
            self.commodity_prices_cache = {}
            for commodity_id in self.commodities_cache.keys():
                try:
                    prices = await self.uex_client.get_commodity_prices(commodity_id)
                    self.commodity_prices_cache[commodity_id] = prices
                except Exception as e:
                    logging.warning(f"Failed to get prices for commodity {commodity_id}: {e}")

            self.cache_expiry = datetime.now() + timedelta(hours=1)
            logging.info("UEX cache refreshed successfully")

        except Exception as e:
            logging.error(f"Failed to refresh UEX cache: {e}")
    
    async def _ensure_cache_fresh(self):
        """Ensure cache is fresh, refresh if needed"""
        if not self.cache_expiry or datetime.now() > self.cache_expiry:
            await self._refresh_cache()
    
    def _format_currency(self, amount: float) -> str:
        """Format currency with proper separators"""
        if amount >= 1000000:
            return f"{amount/1000000:.1f}M aUEC"
        elif amount >= 1000:
            return f"{amount/1000:.1f}K aUEC"
        else:
            return f"{amount:.0f} aUEC"
    
    def _get_commodity_name(self, commodity_id: int) -> str:
        """Get commodity name from cache"""
        commodity = self.commodities_cache.get(commodity_id)
        return commodity['name'] if commodity else f"Unknown ({commodity_id})"
    
    def _get_terminal_name(self, terminal_id: int) -> str:
        """Get terminal name from cache"""
        terminal = self.terminals_cache.get(terminal_id)
        return terminal['name'] if terminal else f"Unknown ({terminal_id})"

    async def vehicle_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete for vehicle names"""
        try:
            await self._ensure_cache_fresh()

            # Filter vehicles that have cargo capacity (using 'scu' field)
            cargo_vehicles = [
                v for v in self.vehicles_cache.values()
                if v.get('scu', 0) > 0
            ]

            # Filter by current input and sort by name
            if current:
                filtered_vehicles = [
                    v for v in cargo_vehicles
                    if current.lower() in v['name'].lower()
                ]
            else:
                filtered_vehicles = cargo_vehicles

            # Sort by name and limit to 25 (Discord limit)
            filtered_vehicles = sorted(filtered_vehicles, key=lambda x: x['name'])[:25]

            return [
                app_commands.Choice(
                    name=f"{vehicle['name']} ({vehicle['scu']} SCU)",
                    value=vehicle['name']
                )
                for vehicle in filtered_vehicles
            ]
        except Exception as e:
            logging.error(f"Error in vehicle autocomplete: {e}")
            return []

    async def _calculate_trade_routes(self, vehicle_scu: int = None, investment_limit: float = None) -> List[Dict]:
        """Calculate trade routes from cached commodity and price data"""
        routes = []

        # Get all tradeable commodities (exclude non-tradeable items)
        excluded_items = [
            'osoian hides', 'ship', 'vehicle', 'component', 'weapon', 'armor', 'suit',
            'helmet', 'backpack', 'tool', 'mining', 'salvage', 'repair', 'fuel',
            'quantum', 'cooler', 'power plant', 'shield', 'thruster'
        ]

        tradeable_commodities = [
            c for c in self.commodities_cache.values()
            if (c.get('is_available') and c.get('is_buyable') and c.get('is_sellable') and
                not any(excluded.lower() in c.get('name', '').lower() for excluded in excluded_items))
        ]

        logging.info(f"Found {len(tradeable_commodities)} tradeable commodities")
        logging.info(f"Commodity prices cache has {len(self.commodity_prices_cache)} entries")

        for commodity in tradeable_commodities:
            commodity_id = commodity['id']
            commodity_name = commodity['name']

            # Get price data for this commodity
            prices = self.commodity_prices_cache.get(commodity_id, [])

            # Create buy and sell location maps
            buy_locations = {}  # terminal_id -> price_data
            sell_locations = {}  # terminal_id -> price_data

            for price_data in prices:
                terminal_id = price_data.get('id_terminal')
                if not terminal_id:
                    continue

                # Check if this location buys the commodity (status_buy > 0 and price_buy > 0)
                if price_data.get('status_buy', 0) > 0 and price_data.get('price_buy', 0) > 0:
                    buy_locations[terminal_id] = price_data

                # Check if this location sells the commodity (status_sell > 0 and price_sell > 0)
                if price_data.get('status_sell', 0) > 0 and price_data.get('price_sell', 0) > 0:
                    sell_locations[terminal_id] = price_data


            # Generate routes by combining buy and sell locations
            for buy_terminal_id, buy_data in buy_locations.items():
                for sell_terminal_id, sell_data in sell_locations.items():
                    if buy_terminal_id == sell_terminal_id:
                        continue  # Skip same location trades

                    buy_price = buy_data.get('price_buy', 0)
                    sell_price = sell_data.get('price_sell', 0)

                    # Validate price data
                    if buy_price <= 0 or sell_price <= 0:
                        continue  # Skip routes with invalid prices

                    if sell_price <= buy_price:
                        continue  # Skip unprofitable routes

                    # Skip routes with unrealistic prices (likely data errors)
                    if buy_price > 5000000 or sell_price > 5000000:  # More than 5M per SCU (increased from 1M)
                        continue

                    profit_per_unit = sell_price - buy_price

                    # Calculate maximum SCU based on constraints
                    max_scu = float('inf')

                    # Apply vehicle SCU limit
                    if vehicle_scu:
                        max_scu = min(max_scu, vehicle_scu)

                    # Apply investment limit (this should be the primary constraint)
                    effective_investment_limit = investment_limit if investment_limit else 2000000
                    if buy_price > 0:
                        max_scu_by_investment = int(effective_investment_limit / buy_price)
                        max_scu = min(max_scu, max_scu_by_investment)

                        # Debug logging for BIOP specifically
                        if commodity_name.lower() == 'bioplastic':
                            logging.info(f"BIOP calculation: buy_price={buy_price}, investment_limit={effective_investment_limit}, max_scu_by_investment={max_scu_by_investment}, vehicle_scu={vehicle_scu}, final_max_scu={max_scu}")

                    if max_scu == float('inf') or max_scu <= 0:
                        continue

                    max_scu = int(max_scu)
                    total_profit = profit_per_unit * max_scu
                    total_investment = buy_price * max_scu
                    roi = (total_profit / total_investment * 100) if total_investment > 0 else 0

                    # Debug logging for suspicious routes
                    if total_profit > 1000000:  # More than 1M profit
                        logging.info(f"High profit route: {commodity_name} - Buy: {buy_price}, Sell: {sell_price}, SCU: {max_scu}, Profit: {total_profit}, Investment: {total_investment}")

                    # Ensure investment doesn't exceed limit (safety check)
                    effective_investment_limit = investment_limit if investment_limit else 2000000
                    if total_investment > effective_investment_limit:
                        logging.warning(f"Investment exceeds limit for {commodity_name}: {total_investment} > {effective_investment_limit}")
                        continue

                    # Create route data structure similar to UEX API
                    route = {
                        'id_commodity': commodity_id,
                        'commodity_name': commodity_name,
                        'origin_terminal_id': buy_terminal_id,
                        'origin_terminal_name': self._get_terminal_name(buy_terminal_id),
                        'destination_terminal_id': sell_terminal_id,
                        'destination_terminal_name': self._get_terminal_name(sell_terminal_id),
                        'price_buy': buy_price,
                        'price_sell': sell_price,
                        'profit': total_profit,
                        'investment': total_investment,
                        'price_roi': roi,
                        'scu': max_scu,
                        'scu_amount': max_scu,
                        'distance': 0  # Distance calculation removed for now
                    }

                    routes.append(route)

        logging.info(f"Generated {len(routes)} total routes")

        # Sort by profit (income) like UEX website
        routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)

        if routes:
            logging.info(f"Top route: {routes[0]['commodity_name']} - Profit: {routes[0]['profit']}")

        return routes



    @app_commands.command(name="traderoutes", description="Display the best trade routes for a specific vehicle and investment")
    @app_commands.describe(
        vehicle="Vehicle name (required, with autocomplete)",
        investment="Maximum investment amount in aUEC (required)"
    )
    @app_commands.autocomplete(vehicle=vehicle_autocomplete)
    async def traderoutes(
        self,
        interaction: discord.Interaction,
        vehicle: str,
        investment: float
    ):
        """Display trade routes with filtering options"""
        try:
            await interaction.response.defer()
            await self._ensure_cache_fresh()

            # Parse vehicle parameter (required)
            vehicle_data = None
            for v in self.vehicles_cache.values():
                if v['name'].lower() == vehicle.lower():
                    vehicle_data = v
                    break

            if not vehicle_data or vehicle_data.get('scu', 0) <= 0:
                embed = discord.Embed(
                    title="❌ Vehicle Not Found",
                    description=f"Vehicle '{vehicle}' not found or has no cargo capacity.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            vehicle_scu = vehicle_data['scu']

            # Validate investment parameter (required)
            if investment <= 0:
                embed = discord.Embed(
                    title="❌ Invalid Investment",
                    description="Investment amount must be greater than 0.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            # Set up filters
            filters = {
                'vehicle': {
                    'name': vehicle_data['name'],
                    'scu': vehicle_scu
                },
                'investment': investment
            }

            # Calculate trade routes from cached data
            routes = await self._calculate_trade_routes(
                vehicle_scu=vehicle_scu,
                investment_limit=investment
            )

            if not routes:
                embed = discord.Embed(
                    title="❌ No Trade Routes Found",
                    description="No trade routes are currently available with the specified criteria.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            # Take top 10 routes (already sorted by profit in calculation method)
            routes = routes[:10]

            embed = self._create_routes_embed(routes, page=1, total_pages=1, filters=filters)
            view = TradeRoutesView(self, routes, filters)

            message = await interaction.followup.send(embed=embed, view=view)
            view.message = message

        except discord.errors.NotFound:
            # Interaction expired, try to send a new message if possible
            logging.warning("Interaction expired while processing traderoutes command")
        except Exception as e:
            logging.error(f"Error in traderoutes command: {e}")
            try:
                embed = discord.Embed(
                    title="❌ Error",
                    description=f"Failed to fetch trade routes: {str(e)}",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
            except:
                logging.error("Failed to send error message - interaction may have expired")



    def _create_routes_embed(self, routes: List[Dict], page: int = 1, total_pages: int = 1, filters: Dict = None) -> discord.Embed:
        """Create embed for trade routes"""
        embed = discord.Embed(
            title="🚀 Best Trade Routes",
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )

        if filters:
            filter_text = []
            if filters.get('commodity'):
                filter_text.append(f"Commodity: {filters['commodity']}")
            if filters.get('origin'):
                filter_text.append(f"Origin: {filters['origin']}")
            if filters.get('destination'):
                filter_text.append(f"Destination: {filters['destination']}")
            if filters.get('investment'):
                filter_text.append(f"Max Investment: {self._format_currency(filters['investment'])}")
            if filters.get('vehicle'):
                vehicle_info = filters['vehicle']
                filter_text.append(f"Vehicle: {vehicle_info['name']} ({vehicle_info['scu']} SCU)")

            if filter_text:
                embed.add_field(name="🔍 Active Filters", value="\n".join(filter_text), inline=False)

        if not routes:
            embed.description = "No routes found matching your criteria."
            return embed

        route_text = []
        for i, route in enumerate(routes, 1):
            commodity_name = self._get_commodity_name(route.get('id_commodity'))
            origin = route.get('origin_terminal_name', 'Unknown') or 'Unknown'
            destination = route.get('destination_terminal_name', 'Unknown') or 'Unknown'
            profit = route.get('profit', 0) or 0
            investment = route.get('investment', 0) or 0
            roi = route.get('price_roi', 0) or 0


            # Use pre-calculated SCU amount if available (from vehicle filtering)
            scu_amount = route.get('scu_amount')
            if scu_amount is None:
                # First try to get SCU directly from route data (UEX API provides this)
                scu_amount = route.get('scu', 0)

                # If not available, calculate from investment and buy price
                if scu_amount == 0:
                    buy_price = route.get('price_buy', 0) or 0
                    if buy_price > 0 and investment > 0:
                        scu_amount = int(investment / buy_price)
                    else:
                        # Last resort: calculate from profit and price difference
                        sell_price = route.get('price_sell', 0) or 0
                        if sell_price > buy_price and buy_price > 0:
                            price_diff = sell_price - buy_price
                            if price_diff > 0:
                                scu_amount = int(profit / price_diff) if profit > 0 else 0

                # If vehicle filter is active, limit SCU to vehicle capacity
                if filters and filters.get('vehicle'):
                    vehicle_scu = filters['vehicle']['scu']
                    if scu_amount > 0:
                        scu_amount = min(scu_amount, vehicle_scu)

            # Ensure all values are not None and reasonable
            scu_amount = max(scu_amount or 0, 0)

            route_text.append(
                f"**{i}.** {commodity_name}\n"
                f"📍 {origin} → {destination}\n"
                f"💰 Profit: {self._format_currency(profit)} | ROI: {roi:.1f}%\n"
                f"📦 {scu_amount} SCU | 💳 Investment: {self._format_currency(investment)}\n"
            )

        embed.description = "\n".join(route_text)
        embed.set_footer(text=f"Page {page}/{total_pages} • Data from UEX Corp")

        return embed

    @app_commands.command(name="commodities", description="Display all commodities with their values and best trading locations")
    async def commodities(self, interaction: discord.Interaction):
        """Display commodities with their values and best locations"""
        await interaction.response.defer()

        try:
            await self._ensure_cache_fresh()

            # Get all commodities (match UEX website - show available and visible commodities)
            commodities = list(self.commodities_cache.values())
            commodities = [c for c in commodities if c.get('is_available') and c.get('is_visible')]
            # Sort by sell price (highest first) since that's most relevant for trading
            commodities = sorted(commodities, key=lambda x: x.get('price_sell', 0), reverse=True)

            if not commodities:
                embed = discord.Embed(
                    title="❌ No Commodities Found",
                    description="No tradeable commodities are currently available.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            embed = self._create_commodities_embed(commodities, page=1)
            view = CommoditiesView(self, commodities)

            await interaction.followup.send(embed=embed, view=view)

        except Exception as e:
            logging.error(f"Error in commodities command: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description=f"Failed to fetch commodities: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed)

    def _create_commodities_embed(self, commodities: List[Dict], page: int = 1) -> discord.Embed:
        """Create embed for commodities list"""
        items_per_page = 10
        start_idx = (page - 1) * items_per_page
        end_idx = start_idx + items_per_page
        page_commodities = commodities[start_idx:end_idx]
        total_pages = (len(commodities) + items_per_page - 1) // items_per_page

        embed = discord.Embed(
            title="📦 Star Citizen Commodities",
            color=discord.Color.gold(),
            timestamp=datetime.now()
        )

        commodity_text = []
        for i, commodity in enumerate(page_commodities, start_idx + 1):
            name = commodity.get('name', 'Unknown')
            buy_price = commodity.get('price_buy', 0)
            sell_price = commodity.get('price_sell', 0)

            # Handle different commodity types
            if buy_price > 0 and sell_price > 0:
                # Regular tradeable commodity
                margin = sell_price - buy_price
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 Buy: {buy_price:.2f} | Sell: {sell_price:.2f} | Margin: {margin:.2f} aUEC/SCU\n"
                )
            elif sell_price > 0:
                # Mineable/harvestable commodity (no buy price)
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 Sell: {sell_price:.2f} aUEC/SCU | ⛏️ Mineable/Harvestable\n"
                )
            elif buy_price > 0:
                # Buyable only (rare case)
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 Buy: {buy_price:.2f} aUEC/SCU | 🛒 Purchase Only\n"
                )
            else:
                # No current pricing data
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 No current pricing data\n"
                )

        embed.description = "\n".join(commodity_text) if commodity_text else "No commodities found."
        embed.set_footer(text=f"Page {page}/{total_pages} • {len(commodities)} total commodities • Data from UEX Corp")

        return embed

    @app_commands.command(name="price", description="Get detailed price information for a specific commodity")
    @app_commands.describe(commodity="The commodity to get price information for")
    async def price(self, interaction: discord.Interaction, commodity: str):
        """Get detailed price information for a commodity"""
        await interaction.response.defer()

        try:
            await self._ensure_cache_fresh()

            # Find commodity by name
            commodity_name = commodity.strip().lower()
            found_commodity = None

            for comm in self.commodities_cache.values():
                if commodity_name in comm['name'].lower() or commodity_name == comm['code'].lower():
                    found_commodity = comm
                    break

            if not found_commodity:
                embed = discord.Embed(
                    title="❌ Commodity Not Found",
                    description=f"Could not find commodity '{commodity}'.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            # Get detailed price information
            prices = await self.uex_client.get_commodity_prices(found_commodity['id'])

            embed = self._create_price_embed(found_commodity, prices)
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logging.error(f"Error in price command: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description=f"Failed to fetch price information: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed)

    @price.autocomplete('commodity')
    async def commodity_autocomplete(self, _: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete for commodity names"""
        try:
            await self._ensure_cache_fresh()

            if not current:
                # Return top 25 most valuable commodities (by sell price)
                commodities = list(self.commodities_cache.values())
                commodities = [c for c in commodities if c.get('is_available') and c.get('is_visible')]
                commodities = sorted(commodities, key=lambda x: x.get('price_sell', 0), reverse=True)[:25]
                return [app_commands.Choice(name=c['name'], value=c['name']) for c in commodities]

            # Filter commodities by current input
            current_lower = current.lower()
            matches = []

            for commodity in self.commodities_cache.values():
                if not commodity.get('is_available') or not commodity.get('is_visible'):
                    continue

                name = commodity['name']
                code = commodity.get('code', '')

                if current_lower in name.lower() or current_lower in code.lower():
                    matches.append(commodity)

                if len(matches) >= 25:
                    break

            return [app_commands.Choice(name=c['name'], value=c['name']) for c in matches]

        except Exception as e:
            logging.error(f"Error in commodity autocomplete: {e}")
            return []

    def _create_price_embed(self, commodity: Dict, prices: List[Dict]) -> discord.Embed:
        """Create detailed price embed for a commodity"""
        embed = discord.Embed(
            title=f"💰 {commodity['name']} Price Information",
            color=discord.Color.gold(),
            timestamp=datetime.now()
        )

        # Basic commodity info
        embed.add_field(
            name="📋 Basic Info",
            value=f"**Code:** {commodity.get('code', 'N/A')}\n"
                  f"**Type:** {commodity.get('kind', 'N/A')}\n"
                  f"**Weight:** {commodity.get('weight_scu', 'N/A')} tons/SCU",
            inline=True
        )

        # Average prices
        buy_price = commodity.get('price_buy', 0)
        sell_price = commodity.get('price_sell', 0)
        margin = sell_price - buy_price
        roi = (margin / buy_price * 100) if buy_price > 0 else 0

        embed.add_field(
            name="💵 Average Prices",
            value=f"**Buy:** {buy_price:.2f} aUEC/SCU\n"
                  f"**Sell:** {sell_price:.2f} aUEC/SCU\n"
                  f"**Margin:** {margin:.2f} aUEC/SCU\n"
                  f"**ROI:** {roi:.1f}%",
            inline=True
        )

        # Commodity properties
        properties = []
        if commodity.get('is_illegal'):
            properties.append("⚠️ Illegal")
        if commodity.get('is_volatile_qt'):
            properties.append("⚡ Volatile (QT)")
        if commodity.get('is_volatile_time'):
            properties.append("⏰ Time Sensitive")
        if commodity.get('is_explosive'):
            properties.append("💥 Explosive")
        if commodity.get('is_harvestable'):
            properties.append("🌾 Harvestable")
        if commodity.get('is_refinable'):
            properties.append("⚙️ Refinable")

        if properties:
            embed.add_field(
                name="🏷️ Properties",
                value="\n".join(properties),
                inline=True
            )

        # Best locations (if price data available)
        if prices:
            # Sort by buy price (lowest first) and sell price (highest first)
            buy_locations = sorted([p for p in prices if p.get('price_buy', 0) > 0],
                                 key=lambda x: x.get('price_buy', 0))[:5]
            sell_locations = sorted([p for p in prices if p.get('price_sell', 0) > 0],
                                  key=lambda x: x.get('price_sell', 0), reverse=True)[:5]

            if buy_locations:
                buy_text = []
                for loc in buy_locations:
                    terminal_name = self._get_terminal_name(loc.get('id_terminal', 0))
                    price = loc.get('price_buy', 0)
                    buy_text.append(f"• {terminal_name}: {price:.2f} aUEC")

                embed.add_field(
                    name="🛒 Best Buy Locations",
                    value="\n".join(buy_text),
                    inline=False
                )

            if sell_locations:
                sell_text = []
                for loc in sell_locations:
                    terminal_name = self._get_terminal_name(loc.get('id_terminal', 0))
                    price = loc.get('price_sell', 0)
                    sell_text.append(f"• {terminal_name}: {price:.2f} aUEC")

                embed.add_field(
                    name="💰 Best Sell Locations",
                    value="\n".join(sell_text),
                    inline=False
                )

        embed.set_footer(text="Data from UEX Corp")
        return embed

class TradeRoutesView(discord.ui.View):
    def __init__(self, cog: UEXTrade, routes: List[Dict], current_filters: Dict):
        super().__init__(timeout=300)
        self.cog = cog
        self.routes = routes
        self.current_filters = current_filters
        self.current_page = 1
        self.routes_per_page = 10
        self.message = None  # Will be set after sending the message

    @discord.ui.button(label="� Refresh", style=discord.ButtonStyle.secondary)
    async def refresh_routes(self, interaction: discord.Interaction, _: discord.ui.Button):
        await interaction.response.defer()

        try:
            # Get fresh routes with the same filters
            vehicle_scu = None
            investment_limit = None

            if self.current_filters.get('vehicle'):
                vehicle_scu = self.current_filters['vehicle']['scu']
            if self.current_filters.get('investment'):
                investment_limit = self.current_filters['investment']

            routes = await self.cog._calculate_trade_routes(
                vehicle_scu=vehicle_scu,
                investment_limit=investment_limit
            )
            routes = routes[:10]

            embed = self.cog._create_routes_embed(routes, page=1, total_pages=1, filters=self.current_filters)
            new_view = TradeRoutesView(self.cog, routes, self.current_filters)

            await interaction.edit_original_response(embed=embed, view=new_view)

        except Exception as e:
            logging.error(f"Error refreshing routes: {e}")
            await interaction.followup.send("❌ Error refreshing routes.", ephemeral=True)

class CommoditiesView(discord.ui.View):
    def __init__(self, cog: UEXTrade, commodities: List[Dict]):
        super().__init__(timeout=300)
        self.cog = cog
        self.commodities = commodities
        self.current_page = 1
        self.items_per_page = 10
        self.total_pages = (len(commodities) + self.items_per_page - 1) // self.items_per_page

        # Disable buttons if only one page
        if self.total_pages <= 1:
            self.previous_page.disabled = True
            self.next_page.disabled = True

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.secondary)
    async def previous_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page > 1:
            self.current_page -= 1
            embed = self.cog._create_commodities_embed(self.commodities, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.secondary)
    async def next_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page < self.total_pages:
            self.current_page += 1
            embed = self.cog._create_commodities_embed(self.commodities, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="🔄 Refresh", style=discord.ButtonStyle.primary)
    async def refresh_data(self, interaction: discord.Interaction, _: discord.ui.Button):
        await interaction.response.defer()

        try:
            # Refresh cache and get updated commodities
            await self.cog._refresh_cache()

            commodities = list(self.cog.commodities_cache.values())
            commodities = [c for c in commodities if c.get('is_available') and c.get('is_visible')]
            commodities = sorted(commodities, key=lambda x: x.get('price_sell', 0), reverse=True)

            self.commodities = commodities
            self.current_page = 1
            self.total_pages = (len(commodities) + self.items_per_page - 1) // self.items_per_page

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages or self.total_pages <= 1

            embed = self.cog._create_commodities_embed(self.commodities, self.current_page)
            await interaction.edit_original_response(embed=embed, view=self)

        except Exception as e:
            logging.error(f"Error refreshing commodities: {e}")
            await interaction.followup.send("❌ Error refreshing data.", ephemeral=True)







class VehicleView(discord.ui.View):
    def __init__(self, cog: UEXTrade, current_filters: Dict = None, trade_routes_view = None):
        super().__init__(timeout=300)
        self.cog = cog
        self.current_filters = current_filters or {}
        self.trade_routes_view = trade_routes_view

    @discord.ui.button(label="🚀 All Vehicles", style=discord.ButtonStyle.primary)
    async def show_all_vehicles(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Show paginated list of all vehicles"""
        await interaction.response.defer()

        try:
            # Get vehicles with cargo capacity
            vehicles = [v for v in self.cog.vehicles_cache.values() if v.get('scu', 0) > 0]
            vehicles = sorted(vehicles, key=lambda x: x.get('scu', 0), reverse=True)

            if not vehicles:
                await interaction.followup.send("❌ No vehicles with cargo capacity found.", ephemeral=True)
                return

            embed = self._create_vehicles_embed(vehicles, page=1)
            view = VehicleListView(self.cog, vehicles, self.current_filters, self.trade_routes_view)

            await interaction.followup.send(embed=embed, view=view, ephemeral=True)

        except Exception as e:
            logging.error(f"Error showing vehicles: {e}")
            await interaction.followup.send("❌ Error loading vehicles.", ephemeral=True)

    @discord.ui.button(label="🔍 Search Vehicle", style=discord.ButtonStyle.secondary)
    async def search_vehicle(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Search for a specific vehicle"""
        modal = VehicleSearchModal(self.cog, self.current_filters, self.trade_routes_view)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="📝 Custom SCU", style=discord.ButtonStyle.secondary)
    async def custom_scu(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Enter custom cargo capacity"""
        modal = CustomSCUModal()
        await interaction.response.send_modal(modal)

    def _create_vehicles_embed(self, vehicles: List[Dict], page: int = 1) -> discord.Embed:
        """Create embed for vehicles list"""
        items_per_page = 15
        start_idx = (page - 1) * items_per_page
        end_idx = start_idx + items_per_page
        page_vehicles = vehicles[start_idx:end_idx]
        total_pages = (len(vehicles) + items_per_page - 1) // items_per_page

        embed = discord.Embed(
            title="🚀 Star Citizen Vehicles",
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )

        vehicle_text = []
        for i, vehicle in enumerate(page_vehicles, start_idx + 1):
            name = vehicle.get('name', 'Unknown')
            cargo = vehicle.get('scu', 0)
            manufacturer = vehicle.get('company_name', 'Unknown')

            vehicle_text.append(f"**{i}.** {name}")
            vehicle_text.append(f"   📦 {cargo} SCU | 🏭 {manufacturer}")
            vehicle_text.append("")  # Empty line for spacing

        embed.description = "\n".join(vehicle_text) if vehicle_text else "No vehicles found."
        embed.set_footer(text=f"Page {page}/{total_pages} • {len(vehicles)} total vehicles • Data from UEX Corp")

        return embed

class VehicleListView(discord.ui.View):
    def __init__(self, cog: UEXTrade, vehicles: List[Dict], current_filters: Dict = None, trade_routes_view = None):
        super().__init__(timeout=300)
        self.cog = cog
        self.vehicles = vehicles
        self.current_filters = current_filters or {}
        self.trade_routes_view = trade_routes_view
        self.current_page = 1
        self.items_per_page = 15
        self.total_pages = (len(vehicles) + self.items_per_page - 1) // self.items_per_page

        # Disable buttons if only one page
        if self.total_pages <= 1:
            self.previous_page.disabled = True
            self.next_page.disabled = True

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.secondary)
    async def previous_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page > 1:
            self.current_page -= 1
            vehicle_view = VehicleView(self.cog)
            embed = vehicle_view._create_vehicles_embed(self.vehicles, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.secondary)
    async def next_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page < self.total_pages:
            self.current_page += 1
            vehicle_view = VehicleView(self.cog)
            embed = vehicle_view._create_vehicles_embed(self.vehicles, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="✅ Select Vehicle", style=discord.ButtonStyle.success)
    async def select_vehicle(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Allow user to select a vehicle by number"""
        modal = VehicleSelectionModal(self.cog, self.vehicles, self.current_filters, self.trade_routes_view)
        await interaction.response.send_modal(modal)

class CustomSCUModal(discord.ui.Modal, title="Custom Cargo Capacity"):
    scu_amount = discord.ui.TextInput(
        label="Cargo Capacity (SCU)",
        placeholder="Enter your vehicle's cargo capacity in SCU",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            scu = int(self.scu_amount.value)
            if scu <= 0:
                raise ValueError("SCU must be positive")

            embed = discord.Embed(
                title="🚀 Custom Vehicle",
                description=f"Cargo Capacity: {scu} SCU",
                color=discord.Color.green()
            )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except ValueError:
            await interaction.response.send_message("❌ Please enter a valid positive number for SCU.", ephemeral=True)

class VehicleSearchModal(discord.ui.Modal, title="Search for Vehicle"):
    def __init__(self, cog: UEXTrade, current_filters: Dict = None, trade_routes_view = None):
        super().__init__()
        self.cog = cog
        self.current_filters = current_filters or {}
        self.trade_routes_view = trade_routes_view

    vehicle_name = discord.ui.TextInput(
        label="Vehicle Name",
        placeholder="Enter vehicle name (e.g., Freelancer, Caterpillar, etc.)",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            search_term = self.vehicle_name.value.strip().lower()

            # Search for vehicles matching the name
            matching_vehicles = []
            for vehicle in self.cog.vehicles_cache.values():
                if (search_term in vehicle.get('name', '').lower() and
                    vehicle.get('scu', 0) > 0):
                    matching_vehicles.append(vehicle)

            if not matching_vehicles:
                await interaction.followup.send(f"❌ No vehicles found matching '{self.vehicle_name.value}'.", ephemeral=True)
                return

            # Sort by cargo capacity
            matching_vehicles = sorted(matching_vehicles, key=lambda x: x.get('scu', 0), reverse=True)

            if len(matching_vehicles) == 1:
                # Single match, show details
                vehicle = matching_vehicles[0]
                embed = discord.Embed(
                    title="🚀 Vehicle Found",
                    description=f"**{vehicle['name']}**\n"
                               f"📦 Cargo: {vehicle.get('scu', 0)} SCU\n"
                               f"🏭 Manufacturer: {vehicle.get('company_name', 'Unknown')}",
                    color=discord.Color.green()
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
            else:
                # Multiple matches, show list
                vehicle_view = VehicleView(self.cog)
                embed = vehicle_view._create_vehicles_embed(matching_vehicles, page=1)
                view = VehicleListView(self.cog, matching_vehicles, self.current_filters, self.trade_routes_view)
                await interaction.followup.send(embed=embed, view=view, ephemeral=True)

        except Exception as e:
            logging.error(f"Error searching vehicles: {e}")
            await interaction.followup.send("❌ Error searching for vehicles.", ephemeral=True)

class VehicleSelectionModal(discord.ui.Modal, title="Select Vehicle by Number"):
    def __init__(self, cog: UEXTrade, vehicles: List[Dict], current_filters: Dict, trade_routes_view):
        super().__init__()
        self.cog = cog
        self.vehicles = vehicles
        self.current_filters = current_filters
        self.trade_routes_view = trade_routes_view

    vehicle_number = discord.ui.TextInput(
        label="Vehicle Number",
        placeholder="Enter the number of the vehicle you want to select",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            vehicle_num = int(self.vehicle_number.value)

            if vehicle_num < 1 or vehicle_num > len(self.vehicles):
                await interaction.response.send_message(
                    f"❌ Please enter a number between 1 and {len(self.vehicles)}.",
                    ephemeral=True
                )
                return

            selected_vehicle = self.vehicles[vehicle_num - 1]

            await interaction.response.defer(ephemeral=True)

            # Apply vehicle filter and get new routes
            new_filters = self.current_filters.copy()
            new_filters['vehicle'] = {
                'name': selected_vehicle['name'],
                'scu': selected_vehicle.get('scu', 0)
            }

            # Get filtered routes with vehicle SCU consideration
            routes = await self._get_filtered_routes(new_filters)

            # Update the trade routes view
            self.trade_routes_view.routes = routes
            self.trade_routes_view.current_filters = new_filters

            embed = self.cog._create_routes_embed(routes, filters=new_filters)
            new_view = TradeRoutesView(self.cog, routes, new_filters)

            # Edit the original trade routes message
            await self.trade_routes_view.message.edit(embed=embed, view=new_view)

            # Send confirmation message
            await interaction.followup.send(
                f"✅ Vehicle **{selected_vehicle['name']}** ({selected_vehicle.get('scu', 0)} SCU) applied to trade routes!",
                ephemeral=True
            )

        except ValueError:
            await interaction.response.send_message("❌ Please enter a valid number.", ephemeral=True)
        except Exception as e:
            logging.error(f"Error selecting vehicle: {e}")
            try:
                await interaction.followup.send("❌ Error selecting vehicle.", ephemeral=True)
            except:
                pass  # Interaction may have expired

    async def _get_filtered_routes(self, filters: Dict) -> List[Dict]:
        """Get routes with vehicle SCU filtering applied"""
        vehicle_scu = filters.get('vehicle', {}).get('scu')
        investment_limit = filters.get('investment')

        # Calculate routes with vehicle and investment constraints
        routes = await self.cog._calculate_trade_routes(
            vehicle_scu=vehicle_scu,
            investment_limit=investment_limit
        )

        # Take top 10 routes
        return routes[:10]

async def setup(bot):
    await bot.add_cog(UEXTrade(bot))
