import discord
from discord.ext import commands
from discord import app_commands
import aiohttp
import asyncio
import os
from typing import Dict, List, Optional, Any
import json
from datetime import datetime, timedelta
import logging

class UEXAPIClient:
    """Client for interacting with UEX API v2.0"""
    
    def __init__(self, api_token: str):
        self.api_token = api_token
        self.base_url = "https://api.uexcorp.space/2.0"
        self.session = None
        self.rate_limit_reset = None
        self.requests_remaining = 14400  # Daily quota
        
    async def _get_session(self):
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            headers = {
                'Authorization': f'Bearer {self.api_token}',
                'User-Agent': 'AegisNoxBot/1.0',
                'Accept': 'application/json'
            }
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """Make API request with rate limiting and error handling"""
        session = await self._get_session()
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with session.get(url, params=params) as response:
                # Update rate limit info
                self.requests_remaining = int(response.headers.get('X-RateLimit-Remaining', self.requests_remaining))
                
                if response.status == 429:
                    # Rate limited
                    retry_after = int(response.headers.get('Retry-After', 60))
                    logging.warning(f"UEX API rate limited. Retry after {retry_after} seconds")
                    await asyncio.sleep(retry_after)
                    return await self._make_request(endpoint, params)
                
                response.raise_for_status()
                data = await response.json()
                
                if data.get('status') != 'ok':
                    raise Exception(f"UEX API Error: {data.get('message', 'Unknown error')}")
                
                return data.get('data', [])
                
        except aiohttp.ClientError as e:
            logging.error(f"UEX API request failed: {e}")
            raise Exception(f"Failed to connect to UEX API: {e}")
    
    async def get_commodities(self) -> List[Dict]:
        """Get all commodities"""
        return await self._make_request('commodities')
    
    async def get_commodity_routes(self, **filters) -> List[Dict]:
        """Get commodity routes with optional filters"""
        # Routes API requires at least one parameter, so if none provided, get all commodities first
        if not filters:
            # Get a popular commodity to show default routes
            commodities = await self.get_commodities()
            if commodities:
                # Find a popular tradeable commodity
                popular_commodity = None
                for commodity in commodities:
                    if (commodity.get('is_available') and commodity.get('is_buyable') and
                        commodity.get('is_sellable') and commodity.get('price_sell', 0) > commodity.get('price_buy', 0)):
                        popular_commodity = commodity
                        break

                if popular_commodity:
                    filters = {'id_commodity': popular_commodity['id']}

        if not filters:
            return []  # Return empty if no valid filters

        return await self._make_request('commodities_routes', params=filters)
    
    async def get_commodity_prices(self, commodity_id: int = None) -> List[Dict]:
        """Get commodity prices"""
        endpoint = 'commodities_prices_all' if commodity_id is None else 'commodities_prices'
        params = {'id_commodity': commodity_id} if commodity_id else None
        return await self._make_request(endpoint, params=params)
    
    async def get_terminals(self) -> List[Dict]:
        """Get all terminals"""
        return await self._make_request('terminals')
    
    async def get_vehicles(self) -> List[Dict]:
        """Get all vehicles"""
        return await self._make_request('vehicles')
    
    async def close(self):
        """Close the session"""
        if self.session and not self.session.closed:
            await self.session.close()

class UEXTrade(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.uex_client = UEXAPIClient(os.getenv('UEX_API_TOKEN'))
        self.commodities_cache = {}
        self.vehicles_cache = {}
        self.terminals_cache = {}
        self.cache_expiry = None
        
    async def cog_load(self):
        """Initialize cache when cog loads"""
        await self._refresh_cache()
        
    async def cog_unload(self):
        """Clean up when cog unloads"""
        await self.uex_client.close()
    
    async def _refresh_cache(self):
        """Refresh cached data from UEX API"""
        try:
            # Cache commodities, vehicles, and terminals for autocomplete and lookups
            commodities = await self.uex_client.get_commodities()
            self.commodities_cache = {c['id']: c for c in commodities}
            
            vehicles = await self.uex_client.get_vehicles()
            self.vehicles_cache = {v['id']: v for v in vehicles}
            
            terminals = await self.uex_client.get_terminals()
            self.terminals_cache = {t['id']: t for t in terminals}
            
            self.cache_expiry = datetime.now() + timedelta(hours=1)
            logging.info("UEX cache refreshed successfully")
            
        except Exception as e:
            logging.error(f"Failed to refresh UEX cache: {e}")
    
    async def _ensure_cache_fresh(self):
        """Ensure cache is fresh, refresh if needed"""
        if not self.cache_expiry or datetime.now() > self.cache_expiry:
            await self._refresh_cache()
    
    def _format_currency(self, amount: float) -> str:
        """Format currency with proper separators"""
        if amount >= 1000000:
            return f"{amount/1000000:.1f}M aUEC"
        elif amount >= 1000:
            return f"{amount/1000:.1f}K aUEC"
        else:
            return f"{amount:.0f} aUEC"
    
    def _get_commodity_name(self, commodity_id: int) -> str:
        """Get commodity name from cache"""
        commodity = self.commodities_cache.get(commodity_id)
        return commodity['name'] if commodity else f"Unknown ({commodity_id})"
    
    def _get_terminal_name(self, terminal_id: int) -> str:
        """Get terminal name from cache"""
        terminal = self.terminals_cache.get(terminal_id)
        return terminal['name'] if terminal else f"Unknown ({terminal_id})"

    @app_commands.command(name="traderoutes", description="Display the best trade routes with interactive filtering")
    async def traderoutes(self, interaction: discord.Interaction):
        """Display trade routes with filtering options"""
        await interaction.response.defer()

        try:
            await self._ensure_cache_fresh()

            # Get routes for top valuable commodities (focus on those with both buy and sell prices for routes)
            routes = []
            commodities = list(self.commodities_cache.values())
            profitable_commodities = [c for c in commodities if c.get('is_available') and c.get('is_buyable') and c.get('is_sellable')]
            profitable_commodities = sorted(profitable_commodities, key=lambda x: x.get('price_sell', 0) - x.get('price_buy', 0), reverse=True)[:5]

            for commodity in profitable_commodities:
                try:
                    commodity_routes = await self.uex_client.get_commodity_routes(id_commodity=commodity['id'])
                    routes.extend(commodity_routes[:2])  # Take top 2 routes per commodity
                except Exception as e:
                    logging.warning(f"Failed to get routes for commodity {commodity['name']}: {e}")
                    continue

            if not routes:
                embed = discord.Embed(
                    title="❌ No Trade Routes Found",
                    description="No trade routes are currently available.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            # Sort by profit and take top 10
            routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)[:10]

            embed = self._create_routes_embed(routes, page=1, total_pages=1)
            view = TradeRoutesView(self, routes, {})

            await interaction.followup.send(embed=embed, view=view)

        except Exception as e:
            logging.error(f"Error in traderoutes command: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description=f"Failed to fetch trade routes: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed)

    def _create_routes_embed(self, routes: List[Dict], page: int = 1, total_pages: int = 1, filters: Dict = None) -> discord.Embed:
        """Create embed for trade routes"""
        embed = discord.Embed(
            title="🚀 Best Trade Routes",
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )

        if filters:
            filter_text = []
            if filters.get('commodity'):
                filter_text.append(f"Commodity: {filters['commodity']}")
            if filters.get('origin'):
                filter_text.append(f"Origin: {filters['origin']}")
            if filters.get('destination'):
                filter_text.append(f"Destination: {filters['destination']}")
            if filters.get('investment'):
                filter_text.append(f"Max Investment: {self._format_currency(filters['investment'])}")

            if filter_text:
                embed.add_field(name="🔍 Active Filters", value="\n".join(filter_text), inline=False)

        if not routes:
            embed.description = "No routes found matching your criteria."
            return embed

        route_text = []
        for i, route in enumerate(routes, 1):
            commodity_name = self._get_commodity_name(route.get('id_commodity'))
            origin = route.get('origin_terminal_name', 'Unknown')
            destination = route.get('destination_terminal_name', 'Unknown')
            profit = route.get('profit', 0)
            investment = route.get('investment', 0)
            roi = route.get('price_roi', 0)
            distance = route.get('distance', 0)

            route_text.append(
                f"**{i}.** {commodity_name}\n"
                f"📍 {origin} → {destination}\n"
                f"💰 Profit: {self._format_currency(profit)} | ROI: {roi:.1f}%\n"
                f"💳 Investment: {self._format_currency(investment)} | 📏 {distance:.1f} GM\n"
            )

        embed.description = "\n".join(route_text)
        embed.set_footer(text=f"Page {page}/{total_pages} • Data from UEX Corp")

        return embed

    @app_commands.command(name="commodities", description="Display all commodities with their values and best trading locations")
    async def commodities(self, interaction: discord.Interaction):
        """Display commodities with their values and best locations"""
        await interaction.response.defer()

        try:
            await self._ensure_cache_fresh()

            # Get all commodities (match UEX website - show available and visible commodities)
            commodities = list(self.commodities_cache.values())
            commodities = [c for c in commodities if c.get('is_available') and c.get('is_visible')]
            # Sort by sell price (highest first) since that's most relevant for trading
            commodities = sorted(commodities, key=lambda x: x.get('price_sell', 0), reverse=True)

            if not commodities:
                embed = discord.Embed(
                    title="❌ No Commodities Found",
                    description="No tradeable commodities are currently available.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            embed = self._create_commodities_embed(commodities, page=1)
            view = CommoditiesView(self, commodities)

            await interaction.followup.send(embed=embed, view=view)

        except Exception as e:
            logging.error(f"Error in commodities command: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description=f"Failed to fetch commodities: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed)

    def _create_commodities_embed(self, commodities: List[Dict], page: int = 1) -> discord.Embed:
        """Create embed for commodities list"""
        items_per_page = 10
        start_idx = (page - 1) * items_per_page
        end_idx = start_idx + items_per_page
        page_commodities = commodities[start_idx:end_idx]
        total_pages = (len(commodities) + items_per_page - 1) // items_per_page

        embed = discord.Embed(
            title="📦 Star Citizen Commodities",
            color=discord.Color.gold(),
            timestamp=datetime.now()
        )

        commodity_text = []
        for i, commodity in enumerate(page_commodities, start_idx + 1):
            name = commodity.get('name', 'Unknown')
            buy_price = commodity.get('price_buy', 0)
            sell_price = commodity.get('price_sell', 0)

            # Handle different commodity types
            if buy_price > 0 and sell_price > 0:
                # Regular tradeable commodity
                margin = sell_price - buy_price
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 Buy: {buy_price:.2f} | Sell: {sell_price:.2f} | Margin: {margin:.2f} aUEC/SCU\n"
                )
            elif sell_price > 0:
                # Mineable/harvestable commodity (no buy price)
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 Sell: {sell_price:.2f} aUEC/SCU | ⛏️ Mineable/Harvestable\n"
                )
            elif buy_price > 0:
                # Buyable only (rare case)
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 Buy: {buy_price:.2f} aUEC/SCU | 🛒 Purchase Only\n"
                )
            else:
                # No current pricing data
                commodity_text.append(
                    f"**{i}.** {name}\n"
                    f"💰 No current pricing data\n"
                )

        embed.description = "\n".join(commodity_text) if commodity_text else "No commodities found."
        embed.set_footer(text=f"Page {page}/{total_pages} • {len(commodities)} total commodities • Data from UEX Corp")

        return embed

    @app_commands.command(name="price", description="Get detailed price information for a specific commodity")
    @app_commands.describe(commodity="The commodity to get price information for")
    async def price(self, interaction: discord.Interaction, commodity: str):
        """Get detailed price information for a commodity"""
        await interaction.response.defer()

        try:
            await self._ensure_cache_fresh()

            # Find commodity by name
            commodity_name = commodity.strip().lower()
            found_commodity = None

            for comm in self.commodities_cache.values():
                if commodity_name in comm['name'].lower() or commodity_name == comm['code'].lower():
                    found_commodity = comm
                    break

            if not found_commodity:
                embed = discord.Embed(
                    title="❌ Commodity Not Found",
                    description=f"Could not find commodity '{commodity}'.",
                    color=discord.Color.red()
                )
                await interaction.followup.send(embed=embed)
                return

            # Get detailed price information
            prices = await self.uex_client.get_commodity_prices(found_commodity['id'])

            embed = self._create_price_embed(found_commodity, prices)
            await interaction.followup.send(embed=embed)

        except Exception as e:
            logging.error(f"Error in price command: {e}")
            embed = discord.Embed(
                title="❌ Error",
                description=f"Failed to fetch price information: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed)

    @price.autocomplete('commodity')
    async def commodity_autocomplete(self, _: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """Autocomplete for commodity names"""
        try:
            await self._ensure_cache_fresh()

            if not current:
                # Return top 25 most valuable commodities (by sell price)
                commodities = list(self.commodities_cache.values())
                commodities = [c for c in commodities if c.get('is_available') and c.get('is_visible')]
                commodities = sorted(commodities, key=lambda x: x.get('price_sell', 0), reverse=True)[:25]
                return [app_commands.Choice(name=c['name'], value=c['name']) for c in commodities]

            # Filter commodities by current input
            current_lower = current.lower()
            matches = []

            for commodity in self.commodities_cache.values():
                if not commodity.get('is_available') or not commodity.get('is_visible'):
                    continue

                name = commodity['name']
                code = commodity.get('code', '')

                if current_lower in name.lower() or current_lower in code.lower():
                    matches.append(commodity)

                if len(matches) >= 25:
                    break

            return [app_commands.Choice(name=c['name'], value=c['name']) for c in matches]

        except Exception as e:
            logging.error(f"Error in commodity autocomplete: {e}")
            return []

    def _create_price_embed(self, commodity: Dict, prices: List[Dict]) -> discord.Embed:
        """Create detailed price embed for a commodity"""
        embed = discord.Embed(
            title=f"💰 {commodity['name']} Price Information",
            color=discord.Color.gold(),
            timestamp=datetime.now()
        )

        # Basic commodity info
        embed.add_field(
            name="📋 Basic Info",
            value=f"**Code:** {commodity.get('code', 'N/A')}\n"
                  f"**Type:** {commodity.get('kind', 'N/A')}\n"
                  f"**Weight:** {commodity.get('weight_scu', 'N/A')} tons/SCU",
            inline=True
        )

        # Average prices
        buy_price = commodity.get('price_buy', 0)
        sell_price = commodity.get('price_sell', 0)
        margin = sell_price - buy_price
        roi = (margin / buy_price * 100) if buy_price > 0 else 0

        embed.add_field(
            name="💵 Average Prices",
            value=f"**Buy:** {buy_price:.2f} aUEC/SCU\n"
                  f"**Sell:** {sell_price:.2f} aUEC/SCU\n"
                  f"**Margin:** {margin:.2f} aUEC/SCU\n"
                  f"**ROI:** {roi:.1f}%",
            inline=True
        )

        # Commodity properties
        properties = []
        if commodity.get('is_illegal'):
            properties.append("⚠️ Illegal")
        if commodity.get('is_volatile_qt'):
            properties.append("⚡ Volatile (QT)")
        if commodity.get('is_volatile_time'):
            properties.append("⏰ Time Sensitive")
        if commodity.get('is_explosive'):
            properties.append("💥 Explosive")
        if commodity.get('is_harvestable'):
            properties.append("🌾 Harvestable")
        if commodity.get('is_refinable'):
            properties.append("⚙️ Refinable")

        if properties:
            embed.add_field(
                name="🏷️ Properties",
                value="\n".join(properties),
                inline=True
            )

        # Best locations (if price data available)
        if prices:
            # Sort by buy price (lowest first) and sell price (highest first)
            buy_locations = sorted([p for p in prices if p.get('price_buy', 0) > 0],
                                 key=lambda x: x.get('price_buy', 0))[:5]
            sell_locations = sorted([p for p in prices if p.get('price_sell', 0) > 0],
                                  key=lambda x: x.get('price_sell', 0), reverse=True)[:5]

            if buy_locations:
                buy_text = []
                for loc in buy_locations:
                    terminal_name = self._get_terminal_name(loc.get('id_terminal', 0))
                    price = loc.get('price_buy', 0)
                    buy_text.append(f"• {terminal_name}: {price:.2f} aUEC")

                embed.add_field(
                    name="🛒 Best Buy Locations",
                    value="\n".join(buy_text),
                    inline=False
                )

            if sell_locations:
                sell_text = []
                for loc in sell_locations:
                    terminal_name = self._get_terminal_name(loc.get('id_terminal', 0))
                    price = loc.get('price_sell', 0)
                    sell_text.append(f"• {terminal_name}: {price:.2f} aUEC")

                embed.add_field(
                    name="💰 Best Sell Locations",
                    value="\n".join(sell_text),
                    inline=False
                )

        embed.set_footer(text="Data from UEX Corp")
        return embed

class TradeRoutesView(discord.ui.View):
    def __init__(self, cog: UEXTrade, routes: List[Dict], current_filters: Dict):
        super().__init__(timeout=300)
        self.cog = cog
        self.routes = routes
        self.current_filters = current_filters
        self.current_page = 1
        self.routes_per_page = 10

    @discord.ui.button(label="🔍 Filter by Commodity", style=discord.ButtonStyle.secondary)
    async def filter_commodity(self, interaction: discord.Interaction, _: discord.ui.Button):
        modal = CommodityFilterModal(self.cog, self.current_filters)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="📍 Filter by Location", style=discord.ButtonStyle.secondary)
    async def filter_location(self, interaction: discord.Interaction, _: discord.ui.Button):
        modal = LocationFilterModal(self.cog, self.current_filters)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="💰 Set Investment", style=discord.ButtonStyle.secondary)
    async def set_investment(self, interaction: discord.Interaction, _: discord.ui.Button):
        modal = InvestmentModal(self.cog, self.current_filters)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="🚀 Vehicle Selection", style=discord.ButtonStyle.primary)
    async def vehicle_selection(self, interaction: discord.Interaction, _: discord.ui.Button):
        view = VehicleView(self.cog)
        await interaction.response.send_message("Choose your vehicle selection method:", view=view, ephemeral=True)

    @discord.ui.button(label="🔄 Clear Filters", style=discord.ButtonStyle.danger)
    async def clear_filters(self, interaction: discord.Interaction, _: discord.ui.Button):
        await interaction.response.defer()

        try:
            # Get fresh routes without filters
            routes = await self.cog.uex_client.get_commodity_routes()
            routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)[:10]

            embed = self.cog._create_routes_embed(routes, page=1, total_pages=1)
            new_view = TradeRoutesView(self.cog, routes, {})

            await interaction.edit_original_response(embed=embed, view=new_view)

        except Exception as e:
            logging.error(f"Error clearing filters: {e}")
            await interaction.followup.send("❌ Error clearing filters.", ephemeral=True)

class CommoditiesView(discord.ui.View):
    def __init__(self, cog: UEXTrade, commodities: List[Dict]):
        super().__init__(timeout=300)
        self.cog = cog
        self.commodities = commodities
        self.current_page = 1
        self.items_per_page = 10
        self.total_pages = (len(commodities) + self.items_per_page - 1) // self.items_per_page

        # Disable buttons if only one page
        if self.total_pages <= 1:
            self.previous_page.disabled = True
            self.next_page.disabled = True

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.secondary)
    async def previous_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page > 1:
            self.current_page -= 1
            embed = self.cog._create_commodities_embed(self.commodities, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.secondary)
    async def next_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page < self.total_pages:
            self.current_page += 1
            embed = self.cog._create_commodities_embed(self.commodities, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="🔄 Refresh", style=discord.ButtonStyle.primary)
    async def refresh_data(self, interaction: discord.Interaction, _: discord.ui.Button):
        await interaction.response.defer()

        try:
            # Refresh cache and get updated commodities
            await self.cog._refresh_cache()

            commodities = list(self.cog.commodities_cache.values())
            commodities = [c for c in commodities if c.get('is_available') and c.get('is_visible')]
            commodities = sorted(commodities, key=lambda x: x.get('price_sell', 0), reverse=True)

            self.commodities = commodities
            self.current_page = 1
            self.total_pages = (len(commodities) + self.items_per_page - 1) // self.items_per_page

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages or self.total_pages <= 1

            embed = self.cog._create_commodities_embed(self.commodities, self.current_page)
            await interaction.edit_original_response(embed=embed, view=self)

        except Exception as e:
            logging.error(f"Error refreshing commodities: {e}")
            await interaction.followup.send("❌ Error refreshing data.", ephemeral=True)

class CommodityFilterModal(discord.ui.Modal, title="Filter by Commodity"):
    def __init__(self, cog: UEXTrade, current_filters: Dict):
        super().__init__()
        self.cog = cog
        self.current_filters = current_filters

    commodity_name = discord.ui.TextInput(
        label="Commodity Name",
        placeholder="Enter commodity name (e.g., Titanium, Gold, etc.)",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            # Find commodity by name
            commodity_name = self.commodity_name.value.strip().lower()
            commodity_id = None

            for cid, commodity in self.cog.commodities_cache.items():
                if commodity_name in commodity['name'].lower():
                    commodity_id = cid
                    break

            if not commodity_id:
                await interaction.followup.send(f"❌ Commodity '{self.commodity_name.value}' not found.", ephemeral=True)
                return

            # Get filtered routes
            filters = {'id_commodity': commodity_id}
            routes = await self.cog.uex_client.get_commodity_routes(**filters)
            routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)[:10]

            new_filters = self.current_filters.copy()
            new_filters['commodity'] = self.cog.commodities_cache[commodity_id]['name']

            embed = self.cog._create_routes_embed(routes, filters=new_filters)
            view = TradeRoutesView(self.cog, routes, new_filters)

            await interaction.edit_original_response(embed=embed, view=view)

        except Exception as e:
            logging.error(f"Error filtering by commodity: {e}")
            await interaction.followup.send("❌ Error applying commodity filter.", ephemeral=True)

class LocationFilterModal(discord.ui.Modal, title="Filter by Location"):
    def __init__(self, cog: UEXTrade, current_filters: Dict):
        super().__init__()
        self.cog = cog
        self.current_filters = current_filters

    origin = discord.ui.TextInput(
        label="Origin Location (Optional)",
        placeholder="Enter origin terminal/location name",
        required=False
    )

    destination = discord.ui.TextInput(
        label="Destination Location (Optional)",
        placeholder="Enter destination terminal/location name",
        required=False
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            filters = {}
            new_filters = self.current_filters.copy()

            # Find origin terminal
            if self.origin.value.strip():
                origin_name = self.origin.value.strip().lower()
                origin_id = None

                for tid, terminal in self.cog.terminals_cache.items():
                    if origin_name in terminal['name'].lower():
                        origin_id = tid
                        break

                if origin_id:
                    filters['id_terminal_origin'] = origin_id
                    new_filters['origin'] = self.cog.terminals_cache[origin_id]['name']

            # Find destination terminal
            if self.destination.value.strip():
                dest_name = self.destination.value.strip().lower()
                dest_id = None

                for tid, terminal in self.cog.terminals_cache.items():
                    if dest_name in terminal['name'].lower():
                        dest_id = tid
                        break

                if dest_id:
                    filters['id_terminal_destination'] = dest_id
                    new_filters['destination'] = self.cog.terminals_cache[dest_id]['name']

            if not filters:
                await interaction.followup.send("❌ No valid locations found.", ephemeral=True)
                return

            # Get filtered routes
            routes = await self.cog.uex_client.get_commodity_routes(**filters)
            routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)[:10]

            embed = self.cog._create_routes_embed(routes, filters=new_filters)
            view = TradeRoutesView(self.cog, routes, new_filters)

            await interaction.edit_original_response(embed=embed, view=view)

        except Exception as e:
            logging.error(f"Error filtering by location: {e}")
            await interaction.followup.send("❌ Error applying location filter.", ephemeral=True)

class InvestmentModal(discord.ui.Modal, title="Set Maximum Investment"):
    def __init__(self, cog: UEXTrade, current_filters: Dict):
        super().__init__()
        self.cog = cog
        self.current_filters = current_filters

    investment = discord.ui.TextInput(
        label="Maximum Investment (aUEC)",
        placeholder="Enter maximum investment amount (e.g., 100000)",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            investment_amount = float(self.investment.value.replace(',', ''))

            if investment_amount <= 0:
                await interaction.followup.send("❌ Investment amount must be positive.", ephemeral=True)
                return

            # Get routes for profitable commodities with investment filter
            routes = []
            commodities = list(self.cog.commodities_cache.values())
            profitable_commodities = [c for c in commodities if c.get('is_available') and c.get('is_buyable') and c.get('is_sellable')]
            profitable_commodities = sorted(profitable_commodities, key=lambda x: x.get('price_sell', 0) - x.get('price_buy', 0), reverse=True)[:10]

            for commodity in profitable_commodities:
                try:
                    filters = {'id_commodity': commodity['id'], 'investment': int(investment_amount)}
                    commodity_routes = await self.cog.uex_client.get_commodity_routes(**filters)
                    routes.extend(commodity_routes[:2])  # Take top 2 routes per commodity
                except Exception as e:
                    logging.warning(f"Failed to get routes for commodity {commodity['name']} with investment filter: {e}")
                    continue

            if not routes:
                await interaction.followup.send(f"❌ No routes found within investment limit of {self.cog._format_currency(investment_amount)}.", ephemeral=True)
                return

            routes = sorted(routes, key=lambda x: x.get('profit', 0), reverse=True)[:10]

            new_filters = self.current_filters.copy()
            new_filters['investment'] = investment_amount

            embed = self.cog._create_routes_embed(routes, filters=new_filters)
            view = TradeRoutesView(self.cog, routes, new_filters)

            await interaction.edit_original_response(embed=embed, view=view)

        except ValueError:
            await interaction.followup.send("❌ Please enter a valid number.", ephemeral=True)
        except Exception as e:
            logging.error(f"Error setting investment filter: {e}")
            await interaction.followup.send("❌ Error applying investment filter.", ephemeral=True)

class VehicleView(discord.ui.View):
    def __init__(self, cog: UEXTrade):
        super().__init__(timeout=300)
        self.cog = cog

    @discord.ui.button(label="🚀 All Vehicles", style=discord.ButtonStyle.primary)
    async def show_all_vehicles(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Show paginated list of all vehicles"""
        await interaction.response.defer()

        try:
            # Get vehicles with cargo capacity
            vehicles = [v for v in self.cog.vehicles_cache.values() if v.get('scu', 0) > 0]
            vehicles = sorted(vehicles, key=lambda x: x.get('scu', 0), reverse=True)

            if not vehicles:
                await interaction.followup.send("❌ No vehicles with cargo capacity found.", ephemeral=True)
                return

            embed = self._create_vehicles_embed(vehicles, page=1)
            view = VehicleListView(self.cog, vehicles)

            await interaction.followup.send(embed=embed, view=view, ephemeral=True)

        except Exception as e:
            logging.error(f"Error showing vehicles: {e}")
            await interaction.followup.send("❌ Error loading vehicles.", ephemeral=True)

    @discord.ui.button(label="🔍 Search Vehicle", style=discord.ButtonStyle.secondary)
    async def search_vehicle(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Search for a specific vehicle"""
        modal = VehicleSearchModal(self.cog)
        await interaction.response.send_modal(modal)

    @discord.ui.button(label="📝 Custom SCU", style=discord.ButtonStyle.secondary)
    async def custom_scu(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Enter custom cargo capacity"""
        modal = CustomSCUModal()
        await interaction.response.send_modal(modal)

    def _create_vehicles_embed(self, vehicles: List[Dict], page: int = 1) -> discord.Embed:
        """Create embed for vehicles list"""
        items_per_page = 15
        start_idx = (page - 1) * items_per_page
        end_idx = start_idx + items_per_page
        page_vehicles = vehicles[start_idx:end_idx]
        total_pages = (len(vehicles) + items_per_page - 1) // items_per_page

        embed = discord.Embed(
            title="🚀 Star Citizen Vehicles",
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )

        vehicle_text = []
        for i, vehicle in enumerate(page_vehicles, start_idx + 1):
            name = vehicle.get('name', 'Unknown')
            cargo = vehicle.get('scu', 0)
            manufacturer = vehicle.get('company_name', 'Unknown')

            vehicle_text.append(f"**{i}.** {name}")
            vehicle_text.append(f"   📦 {cargo} SCU | 🏭 {manufacturer}")
            vehicle_text.append("")  # Empty line for spacing

        embed.description = "\n".join(vehicle_text) if vehicle_text else "No vehicles found."
        embed.set_footer(text=f"Page {page}/{total_pages} • {len(vehicles)} total vehicles • Data from UEX Corp")

        return embed

class VehicleListView(discord.ui.View):
    def __init__(self, cog: UEXTrade, vehicles: List[Dict]):
        super().__init__(timeout=300)
        self.cog = cog
        self.vehicles = vehicles
        self.current_page = 1
        self.items_per_page = 15
        self.total_pages = (len(vehicles) + self.items_per_page - 1) // self.items_per_page

        # Disable buttons if only one page
        if self.total_pages <= 1:
            self.previous_page.disabled = True
            self.next_page.disabled = True

    @discord.ui.button(label="◀️ Previous", style=discord.ButtonStyle.secondary)
    async def previous_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page > 1:
            self.current_page -= 1
            vehicle_view = VehicleView(self.cog)
            embed = vehicle_view._create_vehicles_embed(self.vehicles, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="▶️ Next", style=discord.ButtonStyle.secondary)
    async def next_page(self, interaction: discord.Interaction, _: discord.ui.Button):
        if self.current_page < self.total_pages:
            self.current_page += 1
            vehicle_view = VehicleView(self.cog)
            embed = vehicle_view._create_vehicles_embed(self.vehicles, self.current_page)

            # Update button states
            self.previous_page.disabled = self.current_page <= 1
            self.next_page.disabled = self.current_page >= self.total_pages

            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="✅ Select Vehicle", style=discord.ButtonStyle.success)
    async def select_vehicle(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Allow user to select a vehicle by number"""
        modal = VehicleSelectionModal(self.cog, self.vehicles)
        await interaction.response.send_modal(modal)

class CustomSCUModal(discord.ui.Modal, title="Custom Cargo Capacity"):
    scu_amount = discord.ui.TextInput(
        label="Cargo Capacity (SCU)",
        placeholder="Enter your vehicle's cargo capacity in SCU",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            scu = int(self.scu_amount.value)
            if scu <= 0:
                raise ValueError("SCU must be positive")

            embed = discord.Embed(
                title="🚀 Custom Vehicle",
                description=f"Cargo Capacity: {scu} SCU",
                color=discord.Color.green()
            )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except ValueError:
            await interaction.response.send_message("❌ Please enter a valid positive number for SCU.", ephemeral=True)

class VehicleSearchModal(discord.ui.Modal, title="Search for Vehicle"):
    def __init__(self, cog: UEXTrade):
        super().__init__()
        self.cog = cog

    vehicle_name = discord.ui.TextInput(
        label="Vehicle Name",
        placeholder="Enter vehicle name (e.g., Freelancer, Caterpillar, etc.)",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer()

        try:
            search_term = self.vehicle_name.value.strip().lower()

            # Search for vehicles matching the name
            matching_vehicles = []
            for vehicle in self.cog.vehicles_cache.values():
                if (search_term in vehicle.get('name', '').lower() and
                    vehicle.get('scu', 0) > 0):
                    matching_vehicles.append(vehicle)

            if not matching_vehicles:
                await interaction.followup.send(f"❌ No vehicles found matching '{self.vehicle_name.value}'.", ephemeral=True)
                return

            # Sort by cargo capacity
            matching_vehicles = sorted(matching_vehicles, key=lambda x: x.get('scu', 0), reverse=True)

            if len(matching_vehicles) == 1:
                # Single match, show details
                vehicle = matching_vehicles[0]
                embed = discord.Embed(
                    title="🚀 Vehicle Found",
                    description=f"**{vehicle['name']}**\n"
                               f"📦 Cargo: {vehicle.get('scu', 0)} SCU\n"
                               f"🏭 Manufacturer: {vehicle.get('company_name', 'Unknown')}",
                    color=discord.Color.green()
                )
                await interaction.followup.send(embed=embed, ephemeral=True)
            else:
                # Multiple matches, show list
                vehicle_view = VehicleView(self.cog)
                embed = vehicle_view._create_vehicles_embed(matching_vehicles, page=1)
                view = VehicleListView(self.cog, matching_vehicles)
                await interaction.followup.send(embed=embed, view=view, ephemeral=True)

        except Exception as e:
            logging.error(f"Error searching vehicles: {e}")
            await interaction.followup.send("❌ Error searching for vehicles.", ephemeral=True)

class VehicleSelectionModal(discord.ui.Modal, title="Select Vehicle by Number"):
    def __init__(self, cog: UEXTrade, vehicles: List[Dict]):
        super().__init__()
        self.cog = cog
        self.vehicles = vehicles

    vehicle_number = discord.ui.TextInput(
        label="Vehicle Number",
        placeholder="Enter the number of the vehicle you want to select",
        required=True
    )

    async def on_submit(self, interaction: discord.Interaction):
        try:
            vehicle_num = int(self.vehicle_number.value)

            if vehicle_num < 1 or vehicle_num > len(self.vehicles):
                await interaction.response.send_message(
                    f"❌ Please enter a number between 1 and {len(self.vehicles)}.",
                    ephemeral=True
                )
                return

            selected_vehicle = self.vehicles[vehicle_num - 1]

            embed = discord.Embed(
                title="🚀 Vehicle Selected",
                description=f"**{selected_vehicle['name']}**\n"
                           f"📦 Cargo: {selected_vehicle.get('scu', 0)} SCU\n"
                           f"🏭 Manufacturer: {selected_vehicle.get('company_name', 'Unknown')}",
                color=discord.Color.green()
            )

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except ValueError:
            await interaction.response.send_message("❌ Please enter a valid number.", ephemeral=True)
        except Exception as e:
            logging.error(f"Error selecting vehicle: {e}")
            await interaction.response.send_message("❌ Error selecting vehicle.", ephemeral=True)

async def setup(bot):
    await bot.add_cog(UEXTrade(bot))
