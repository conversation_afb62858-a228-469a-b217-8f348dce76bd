2025-07-14 00:02:43,063 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:02:43,064 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:02:43,066 [INFO] Loaded cog: embedgen.py
2025-07-14 00:02:43,067 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:02:43,070 [INFO] Loaded cog: giveaway.py
2025-07-14 00:02:43,073 [INFO] Loaded cog: moderation.py
2025-07-14 00:02:43,075 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:02:43,076 [INFO] Loaded cog: rosters.py
2025-07-14 00:02:43,078 [INFO] Loaded cog: shipgame.py
2025-07-14 00:02:43,080 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:02:43,109 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_data_extract.start()
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

2025-07-14 00:02:43,113 [INFO] Loaded cog: voice.py
2025-07-14 00:02:43,115 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:02:43,116 [INFO] Loaded cog: welcome.py
2025-07-14 00:02:43,116 [INFO] logging in using static token
2025-07-14 00:02:43,986 [INFO] Shard ID None has connected to Gateway (Session ID: 53235a05cb24072828a0b7610bf2fbb0).
2025-07-14 00:02:46,010 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:02:46,226 [INFO] Synced 29 slash commands.
2025-07-14 00:03:43,782 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:03:43,785 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:03:43,786 [INFO] Loaded cog: embedgen.py
2025-07-14 00:03:43,788 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:03:43,791 [INFO] Loaded cog: giveaway.py
2025-07-14 00:03:43,793 [INFO] Loaded cog: moderation.py
2025-07-14 00:03:43,794 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:03:43,796 [INFO] Loaded cog: rosters.py
2025-07-14 00:03:43,797 [INFO] Loaded cog: shipgame.py
2025-07-14 00:03:43,797 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:03:43,814 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_uex_data.start()
    ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_uex_data'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_uex_data'

2025-07-14 00:03:43,816 [INFO] Loaded cog: voice.py
2025-07-14 00:03:43,821 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:03:43,822 [INFO] Loaded cog: welcome.py
2025-07-14 00:03:43,822 [INFO] logging in using static token
2025-07-14 00:03:44,532 [INFO] Shard ID None has connected to Gateway (Session ID: 4a686e7a3a6d1921b1f41af53d1c2b02).
2025-07-14 00:03:46,543 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:03:46,796 [INFO] Synced 29 slash commands.
2025-07-14 00:04:43,880 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:04:43,882 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:04:43,883 [INFO] Loaded cog: embedgen.py
2025-07-14 00:04:43,885 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:04:43,887 [INFO] Loaded cog: giveaway.py
2025-07-14 00:04:43,890 [INFO] Loaded cog: moderation.py
2025-07-14 00:04:43,891 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:04:43,892 [INFO] Loaded cog: rosters.py
2025-07-14 00:04:43,893 [INFO] Loaded cog: shipgame.py
2025-07-14 00:04:43,894 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:04:43,910 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
                     ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 105, in __init__
    self.update_data_extract.start()
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'update_data_extract'

2025-07-14 00:04:43,912 [INFO] Loaded cog: voice.py
2025-07-14 00:04:43,914 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:04:43,915 [INFO] Loaded cog: welcome.py
2025-07-14 00:04:43,915 [INFO] logging in using static token
2025-07-14 00:04:44,718 [INFO] Shard ID None has connected to Gateway (Session ID: 4a16389f58a0c8ac542c5a9ff60607ea).
2025-07-14 00:04:46,738 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:04:47,051 [INFO] Synced 29 slash commands.
2025-07-14 00:05:55,622 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:05:55,623 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:05:55,625 [INFO] Loaded cog: embedgen.py
2025-07-14 00:05:55,626 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:05:55,629 [INFO] Loaded cog: giveaway.py
2025-07-14 00:05:55,631 [INFO] Loaded cog: moderation.py
2025-07-14 00:05:55,633 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:05:55,634 [INFO] Loaded cog: rosters.py
2025-07-14 00:05:55,636 [INFO] Loaded cog: shipgame.py
2025-07-14 00:05:55,637 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:05:55,656 [ERROR] Failed to load cog traderoutes.py: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 947, in _load_from_module_spec
    await setup(self)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 1055, in setup
    await bot.add_cog(TradeRoutes(bot))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 783, in add_cog
    cog = await cog._inject(self, override=override, guild=guild, guilds=guilds)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\cog.py", line 684, in _inject
    await maybe_coroutine(self.cog_load)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\utils.py", line 693, in maybe_coroutine
    return await value
           ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 241, in cog_load
    await self.update_data_extract()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 368, in __call__
    return await self.coro(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 232, in update_data_extract
    await self.fetch_and_cache_commodities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\main.py", line 57, in load_cogs
    await bot.load_extension(f'cogs.{filename[:-3]}')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 1013, in load_extension
    await self._load_from_module_spec(spec, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\commands\bot.py", line 952, in _load_from_module_spec
    raise errors.ExtensionFailed(key, e) from e
discord.ext.commands.errors.ExtensionFailed: Extension 'cogs.traderoutes' raised an error: AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'

2025-07-14 00:05:55,658 [INFO] Loaded cog: voice.py
2025-07-14 00:05:55,660 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:05:55,660 [INFO] Loaded cog: welcome.py
2025-07-14 00:05:55,660 [INFO] logging in using static token
2025-07-14 00:05:55,665 [ERROR] Unhandled exception in internal background task 'update_data_extract'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ext\tasks\__init__.py", line 239, in _loop
    await self.coro(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\traderoutes.py", line 232, in update_data_extract
    await self.fetch_and_cache_commodities()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradeRoutes' object has no attribute 'fetch_and_cache_commodities'
2025-07-14 00:05:56,404 [INFO] Shard ID None has connected to Gateway (Session ID: fc2e8a068383a40ea97998683c432470).
2025-07-14 00:05:58,414 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:05:58,813 [INFO] Synced 29 slash commands.
2025-07-14 00:07:38,092 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:07:38,094 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:07:38,095 [INFO] Loaded cog: embedgen.py
2025-07-14 00:07:38,097 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:07:38,100 [INFO] Loaded cog: giveaway.py
2025-07-14 00:07:38,103 [INFO] Loaded cog: moderation.py
2025-07-14 00:07:38,104 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:07:38,105 [INFO] Loaded cog: rosters.py
2025-07-14 00:07:38,107 [INFO] Loaded cog: shipgame.py
2025-07-14 00:07:38,108 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:07:38,121 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:07:38,123 [INFO] Loaded cog: voice.py
2025-07-14 00:07:38,125 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:07:38,126 [INFO] Loaded cog: welcome.py
2025-07-14 00:07:38,126 [INFO] logging in using static token
2025-07-14 00:07:38,892 [INFO] Shard ID None has connected to Gateway (Session ID: a69c183e7ed1f9274b1e40addf3ab11e).
2025-07-14 00:07:40,918 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:07:41,137 [INFO] Synced 29 slash commands.
2025-07-14 00:08:55,988 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:08:55,989 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:08:55,991 [INFO] Loaded cog: embedgen.py
2025-07-14 00:08:55,992 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:08:55,997 [INFO] Loaded cog: giveaway.py
2025-07-14 00:08:56,000 [INFO] Loaded cog: moderation.py
2025-07-14 00:08:56,001 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:08:56,003 [INFO] Loaded cog: rosters.py
2025-07-14 00:08:56,004 [INFO] Loaded cog: shipgame.py
2025-07-14 00:08:56,004 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:08:56,008 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:08:56,009 [INFO] Loaded cog: voice.py
2025-07-14 00:08:56,011 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:08:56,011 [INFO] Loaded cog: welcome.py
2025-07-14 00:08:56,011 [INFO] logging in using static token
2025-07-14 00:08:56,859 [INFO] Shard ID None has connected to Gateway (Session ID: 9c2adf7cd23e7363a724405e595605be).
2025-07-14 00:08:58,876 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:08:59,130 [INFO] Synced 29 slash commands.
2025-07-14 00:10:55,059 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:10:55,060 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:10:55,061 [INFO] Loaded cog: embedgen.py
2025-07-14 00:10:55,064 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:10:55,066 [INFO] Loaded cog: giveaway.py
2025-07-14 00:10:55,071 [INFO] Loaded cog: moderation.py
2025-07-14 00:10:55,072 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:10:55,074 [INFO] Loaded cog: rosters.py
2025-07-14 00:10:55,075 [INFO] Loaded cog: shipgame.py
2025-07-14 00:10:55,076 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:10:55,089 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:10:55,091 [INFO] Loaded cog: voice.py
2025-07-14 00:10:55,093 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:10:55,093 [INFO] Loaded cog: welcome.py
2025-07-14 00:10:55,094 [INFO] logging in using static token
2025-07-14 00:10:55,988 [INFO] Shard ID None has connected to Gateway (Session ID: 66714dd0b343cd97125b64580d51d80c).
2025-07-14 00:10:58,002 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:10:58,232 [INFO] Synced 29 slash commands.
2025-07-14 00:13:00,574 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:13:00,576 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:13:00,577 [INFO] Loaded cog: embedgen.py
2025-07-14 00:13:00,578 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:13:00,581 [INFO] Loaded cog: giveaway.py
2025-07-14 00:13:00,583 [INFO] Loaded cog: moderation.py
2025-07-14 00:13:00,584 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:13:00,586 [INFO] Loaded cog: rosters.py
2025-07-14 00:13:00,587 [INFO] Loaded cog: shipgame.py
2025-07-14 00:13:00,588 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:13:00,591 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:13:00,593 [INFO] Loaded cog: voice.py
2025-07-14 00:13:00,595 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:13:00,595 [INFO] Loaded cog: welcome.py
2025-07-14 00:13:00,595 [INFO] logging in using static token
2025-07-14 00:13:02,849 [INFO] Shard ID None has connected to Gateway (Session ID: 59003e59c259dd8f8517e19ae720b587).
2025-07-14 00:13:04,867 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:13:05,140 [INFO] Synced 29 slash commands globally.
2025-07-14 00:13:05,140 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:15:00,752 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:15:00,754 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:15:00,756 [INFO] Loaded cog: embedgen.py
2025-07-14 00:15:00,757 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:15:00,760 [INFO] Loaded cog: giveaway.py
2025-07-14 00:15:00,763 [INFO] Loaded cog: moderation.py
2025-07-14 00:15:00,767 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:15:00,769 [INFO] Loaded cog: rosters.py
2025-07-14 00:15:00,771 [INFO] Loaded cog: shipgame.py
2025-07-14 00:15:00,772 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:15:00,784 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:15:00,786 [INFO] Loaded cog: voice.py
2025-07-14 00:15:00,788 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:15:00,788 [INFO] Loaded cog: welcome.py
2025-07-14 00:15:00,788 [INFO] logging in using static token
2025-07-14 00:15:01,688 [INFO] Shard ID None has connected to Gateway (Session ID: b064717cb48878e7d451f110bba4f01b).
2025-07-14 00:15:03,704 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:15:04,104 [INFO] Synced 31 slash commands globally.
2025-07-14 00:15:04,105 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:16:54,552 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:16:54,554 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:16:54,555 [INFO] Loaded cog: embedgen.py
2025-07-14 00:16:54,556 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:16:54,560 [INFO] Loaded cog: giveaway.py
2025-07-14 00:16:54,562 [INFO] Loaded cog: moderation.py
2025-07-14 00:16:54,563 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:16:54,565 [INFO] Loaded cog: rosters.py
2025-07-14 00:16:54,566 [INFO] Loaded cog: shipgame.py
2025-07-14 00:16:54,567 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:16:54,581 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:16:54,582 [INFO] Loaded cog: voice.py
2025-07-14 00:16:54,584 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:16:54,584 [INFO] Loaded cog: welcome.py
2025-07-14 00:16:54,584 [INFO] logging in using static token
2025-07-14 00:16:55,266 [INFO] Shard ID None has connected to Gateway (Session ID: 042c1de51c64e1b2aeb2254566ae70d3).
2025-07-14 00:16:57,275 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:16:57,526 [INFO] Synced 31 slash commands globally.
2025-07-14 00:16:57,526 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:18:34,291 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:18:34,292 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:18:34,294 [INFO] Loaded cog: embedgen.py
2025-07-14 00:18:34,295 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:18:34,298 [INFO] Loaded cog: giveaway.py
2025-07-14 00:18:34,300 [INFO] Loaded cog: moderation.py
2025-07-14 00:18:34,301 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:18:34,303 [INFO] Loaded cog: rosters.py
2025-07-14 00:18:34,304 [INFO] Loaded cog: shipgame.py
2025-07-14 00:18:34,305 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:18:34,318 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:18:34,319 [INFO] Loaded cog: voice.py
2025-07-14 00:18:34,322 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:18:34,322 [INFO] Loaded cog: welcome.py
2025-07-14 00:18:34,322 [INFO] logging in using static token
2025-07-14 00:18:35,094 [INFO] Shard ID None has connected to Gateway (Session ID: 27558d3bdd4aadc5a4e72362496c1f47).
2025-07-14 00:18:37,121 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:18:37,359 [INFO] Synced 31 slash commands globally.
2025-07-14 00:18:37,360 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:20:13,252 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:20:13,254 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:20:13,255 [INFO] Loaded cog: embedgen.py
2025-07-14 00:20:13,256 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:20:13,260 [INFO] Loaded cog: giveaway.py
2025-07-14 00:20:13,262 [INFO] Loaded cog: moderation.py
2025-07-14 00:20:13,263 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:20:13,268 [INFO] Loaded cog: rosters.py
2025-07-14 00:20:13,269 [INFO] Loaded cog: shipgame.py
2025-07-14 00:20:13,270 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:20:13,284 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:20:13,285 [INFO] Loaded cog: voice.py
2025-07-14 00:20:13,287 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:20:13,288 [INFO] Loaded cog: welcome.py
2025-07-14 00:20:13,288 [INFO] logging in using static token
2025-07-14 00:20:15,281 [INFO] Shard ID None has connected to Gateway (Session ID: 9e18abb38a7ebda40763144a55b4f397).
2025-07-14 00:20:17,307 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:20:17,499 [INFO] Synced 31 slash commands globally.
2025-07-14 00:20:17,499 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:24:19,729 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:24:19,730 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:24:19,731 [INFO] Loaded cog: embedgen.py
2025-07-14 00:24:19,733 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:24:19,736 [INFO] Loaded cog: giveaway.py
2025-07-14 00:24:19,738 [INFO] Loaded cog: moderation.py
2025-07-14 00:24:19,739 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:24:19,741 [INFO] Loaded cog: rosters.py
2025-07-14 00:24:19,742 [INFO] Loaded cog: shipgame.py
2025-07-14 00:24:19,744 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:24:19,756 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:24:19,757 [INFO] Loaded cog: voice.py
2025-07-14 00:24:19,759 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:24:19,759 [INFO] Loaded cog: welcome.py
2025-07-14 00:24:19,759 [INFO] logging in using static token
2025-07-14 00:24:20,489 [INFO] Shard ID None has connected to Gateway (Session ID: 74b4f629f14979abad520a44cb64504e).
2025-07-14 00:24:22,504 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:24:22,799 [INFO] Synced 31 slash commands globally.
2025-07-14 00:24:22,799 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:27:31,875 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:27:31,877 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:27:31,878 [INFO] Loaded cog: embedgen.py
2025-07-14 00:27:31,880 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:27:31,883 [INFO] Loaded cog: giveaway.py
2025-07-14 00:27:31,886 [INFO] Loaded cog: moderation.py
2025-07-14 00:27:31,887 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:27:31,890 [INFO] Loaded cog: rosters.py
2025-07-14 00:27:31,891 [INFO] Loaded cog: shipgame.py
2025-07-14 00:27:31,891 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:27:31,905 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:27:31,907 [INFO] Loaded cog: voice.py
2025-07-14 00:27:31,909 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:27:31,909 [INFO] Loaded cog: welcome.py
2025-07-14 00:27:31,909 [INFO] logging in using static token
2025-07-14 00:27:32,674 [INFO] Shard ID None has connected to Gateway (Session ID: 2f31ff44564ebabbff08a4f89ed5a28f).
2025-07-14 00:27:34,692 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:27:34,939 [INFO] Synced 31 slash commands globally.
2025-07-14 00:27:34,939 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:32:01,752 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:32:01,754 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:32:01,755 [INFO] Loaded cog: embedgen.py
2025-07-14 00:32:01,757 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:32:01,760 [INFO] Loaded cog: giveaway.py
2025-07-14 00:32:01,763 [INFO] Loaded cog: moderation.py
2025-07-14 00:32:01,764 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:32:01,765 [INFO] Loaded cog: rosters.py
2025-07-14 00:32:01,767 [INFO] Loaded cog: shipgame.py
2025-07-14 00:32:01,768 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:32:01,786 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:32:01,787 [INFO] Loaded cog: voice.py
2025-07-14 00:32:01,790 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:32:01,791 [INFO] Loaded cog: welcome.py
2025-07-14 00:32:01,791 [INFO] logging in using static token
2025-07-14 00:32:02,574 [INFO] Shard ID None has connected to Gateway (Session ID: c76d2a64f608b988193a2993b50d12dd).
2025-07-14 00:32:04,582 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:32:04,813 [INFO] Synced 31 slash commands globally.
2025-07-14 00:32:04,813 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:34:11,113 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:34:11,114 [INFO] Loaded cog: commodityprices.py
2025-07-14 00:34:11,115 [INFO] Loaded cog: embedgen.py
2025-07-14 00:34:11,117 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:34:11,119 [INFO] Loaded cog: giveaway.py
2025-07-14 00:34:11,124 [INFO] Loaded cog: moderation.py
2025-07-14 00:34:11,127 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:34:11,129 [INFO] Loaded cog: rosters.py
2025-07-14 00:34:11,130 [INFO] Loaded cog: shipgame.py
2025-07-14 00:34:11,131 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:34:11,148 [INFO] Loaded cog: traderoutes.py
2025-07-14 00:34:11,150 [INFO] Loaded cog: voice.py
2025-07-14 00:34:11,152 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:34:11,152 [INFO] Loaded cog: welcome.py
2025-07-14 00:34:11,152 [INFO] logging in using static token
2025-07-14 00:34:11,993 [INFO] Shard ID None has connected to Gateway (Session ID: f2af4d9dcc87da2b93e42835788156e8).
2025-07-14 00:34:14,024 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:34:14,452 [INFO] Synced 31 slash commands globally.
2025-07-14 00:34:14,452 [INFO] Registered commands: ['prices', 'price', 'update_prices', 'embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'test-routes', 'traderoutes', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:54:45,955 [INFO] Loaded cog: applytoaegis.py
2025-07-14 00:54:45,956 [INFO] Loaded cog: embedgen.py
2025-07-14 00:54:45,957 [INFO] Loaded cog: fleetdata.py
2025-07-14 00:54:45,960 [INFO] Loaded cog: giveaway.py
2025-07-14 00:54:45,963 [INFO] Loaded cog: moderation.py
2025-07-14 00:54:45,964 [INFO] Loaded cog: rolerequest.py
2025-07-14 00:54:45,966 [INFO] Loaded cog: rosters.py
2025-07-14 00:54:45,967 [INFO] Loaded cog: shipgame.py
2025-07-14 00:54:45,968 [INFO] Loaded cog: storagetracker.py
2025-07-14 00:54:46,438 [INFO] UEX cache refreshed successfully
2025-07-14 00:54:46,438 [INFO] Loaded cog: uex_trade.py
2025-07-14 00:54:46,440 [INFO] Loaded cog: voice.py
2025-07-14 00:54:46,444 [INFO] Loaded cog: voicetracker.py
2025-07-14 00:54:46,444 [INFO] Loaded cog: welcome.py
2025-07-14 00:54:46,445 [INFO] logging in using static token
2025-07-14 00:54:47,586 [INFO] Shard ID None has connected to Gateway (Session ID: 687fb240cc9274d00cebc24284896c44).
2025-07-14 00:54:49,595 [INFO] Logged in as Aegis Nox Bot
2025-07-14 00:54:50,072 [INFO] Synced 29 slash commands globally.
2025-07-14 00:54:50,073 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 00:55:37,000 [ERROR] UEX API request failed: 400, message='Bad Request', url='https://api.uexcorp.space/2.0/commodities_routes?investment=2000000'
2025-07-14 00:55:37,001 [ERROR] Error setting investment filter: Failed to connect to UEX API: 400, message='Bad Request', url='https://api.uexcorp.space/2.0/commodities_routes?investment=2000000'
2025-07-14 00:57:09,011 [INFO] UEX cache refreshed successfully
2025-07-14 01:01:42,035 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:01:42,036 [INFO] Loaded cog: embedgen.py
2025-07-14 01:01:42,037 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:01:42,040 [INFO] Loaded cog: giveaway.py
2025-07-14 01:01:42,045 [INFO] Loaded cog: moderation.py
2025-07-14 01:01:42,046 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:01:42,047 [INFO] Loaded cog: rosters.py
2025-07-14 01:01:42,048 [INFO] Loaded cog: shipgame.py
2025-07-14 01:01:42,049 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:01:42,465 [INFO] UEX cache refreshed successfully
2025-07-14 01:01:42,465 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:01:42,466 [INFO] Loaded cog: voice.py
2025-07-14 01:01:42,469 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:01:42,469 [INFO] Loaded cog: welcome.py
2025-07-14 01:01:42,469 [INFO] logging in using static token
2025-07-14 01:01:43,276 [INFO] Shard ID None has connected to Gateway (Session ID: e14dc2e153bf6ba72cb13e4f89fb9b4e).
2025-07-14 01:01:45,282 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:01:45,524 [INFO] Synced 29 slash commands globally.
2025-07-14 01:01:45,524 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:05:17,406 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:05:17,408 [INFO] Loaded cog: embedgen.py
2025-07-14 01:05:17,409 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:05:17,411 [INFO] Loaded cog: giveaway.py
2025-07-14 01:05:17,414 [INFO] Loaded cog: moderation.py
2025-07-14 01:05:17,415 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:05:17,417 [INFO] Loaded cog: rosters.py
2025-07-14 01:05:17,418 [INFO] Loaded cog: shipgame.py
2025-07-14 01:05:17,418 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:05:17,798 [INFO] UEX cache refreshed successfully
2025-07-14 01:05:17,798 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:05:17,800 [INFO] Loaded cog: voice.py
2025-07-14 01:05:17,803 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:05:17,804 [INFO] Loaded cog: welcome.py
2025-07-14 01:05:17,804 [INFO] logging in using static token
2025-07-14 01:05:18,699 [INFO] Shard ID None has connected to Gateway (Session ID: b9d564e1323fbdb6798a85ff938a86e3).
2025-07-14 01:05:20,711 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:05:21,077 [INFO] Synced 29 slash commands globally.
2025-07-14 01:05:21,077 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:07:48,494 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:07:48,495 [INFO] Loaded cog: embedgen.py
2025-07-14 01:07:48,497 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:07:48,499 [INFO] Loaded cog: giveaway.py
2025-07-14 01:07:48,502 [INFO] Loaded cog: moderation.py
2025-07-14 01:07:48,504 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:07:48,506 [INFO] Loaded cog: rosters.py
2025-07-14 01:07:48,508 [INFO] Loaded cog: shipgame.py
2025-07-14 01:07:48,509 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:07:48,942 [INFO] UEX cache refreshed successfully
2025-07-14 01:07:48,942 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:07:48,944 [INFO] Loaded cog: voice.py
2025-07-14 01:07:48,947 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:07:48,947 [INFO] Loaded cog: welcome.py
2025-07-14 01:07:48,948 [INFO] logging in using static token
2025-07-14 01:07:50,021 [INFO] Shard ID None has connected to Gateway (Session ID: eba2173641665274bbedd96ff72644ba).
2025-07-14 01:07:52,041 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:07:52,363 [INFO] Synced 29 slash commands globally.
2025-07-14 01:07:52,363 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:09:01,588 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:09:01,590 [INFO] Loaded cog: embedgen.py
2025-07-14 01:09:01,592 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:09:01,594 [INFO] Loaded cog: giveaway.py
2025-07-14 01:09:01,597 [INFO] Loaded cog: moderation.py
2025-07-14 01:09:01,599 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:09:01,600 [INFO] Loaded cog: rosters.py
2025-07-14 01:09:01,601 [INFO] Loaded cog: shipgame.py
2025-07-14 01:09:01,602 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:09:02,066 [INFO] UEX cache refreshed successfully
2025-07-14 01:09:02,066 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:09:02,068 [INFO] Loaded cog: voice.py
2025-07-14 01:09:02,071 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:09:02,072 [INFO] Loaded cog: welcome.py
2025-07-14 01:09:02,072 [INFO] logging in using static token
2025-07-14 01:09:03,002 [INFO] Shard ID None has connected to Gateway (Session ID: a1fbf9134cb3a26bb4f8c912e6f66a6c).
2025-07-14 01:09:05,024 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:09:05,257 [INFO] Synced 29 slash commands globally.
2025-07-14 01:09:05,257 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:09:49,047 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:09:49,048 [INFO] Loaded cog: embedgen.py
2025-07-14 01:09:49,049 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:09:49,051 [INFO] Loaded cog: giveaway.py
2025-07-14 01:09:49,054 [INFO] Loaded cog: moderation.py
2025-07-14 01:09:49,055 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:09:49,057 [INFO] Loaded cog: rosters.py
2025-07-14 01:09:49,058 [INFO] Loaded cog: shipgame.py
2025-07-14 01:09:49,058 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:09:49,476 [INFO] UEX cache refreshed successfully
2025-07-14 01:09:49,476 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:09:49,478 [INFO] Loaded cog: voice.py
2025-07-14 01:09:49,481 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:09:49,481 [INFO] Loaded cog: welcome.py
2025-07-14 01:09:49,481 [INFO] logging in using static token
2025-07-14 01:09:50,195 [INFO] Shard ID None has connected to Gateway (Session ID: 407fa59029ae2bccbe15df317d164619).
2025-07-14 01:09:52,215 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:09:52,547 [INFO] Synced 29 slash commands globally.
2025-07-14 01:09:52,548 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:19:41,613 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:19:41,615 [INFO] Loaded cog: embedgen.py
2025-07-14 01:19:41,616 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:19:41,618 [INFO] Loaded cog: giveaway.py
2025-07-14 01:19:41,623 [INFO] Loaded cog: moderation.py
2025-07-14 01:19:41,624 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:19:41,625 [INFO] Loaded cog: rosters.py
2025-07-14 01:19:41,627 [INFO] Loaded cog: shipgame.py
2025-07-14 01:19:41,627 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:19:42,071 [INFO] UEX cache refreshed successfully
2025-07-14 01:19:42,071 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:19:42,073 [INFO] Loaded cog: voice.py
2025-07-14 01:19:42,076 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:19:42,077 [INFO] Loaded cog: welcome.py
2025-07-14 01:19:42,077 [INFO] logging in using static token
2025-07-14 01:19:42,905 [INFO] Shard ID None has connected to Gateway (Session ID: 75eecd8ae995439dfcd138a1f8a59f4d).
2025-07-14 01:19:44,912 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:19:45,105 [INFO] Synced 29 slash commands globally.
2025-07-14 01:19:45,105 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:20:41,074 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:20:41,075 [INFO] Loaded cog: embedgen.py
2025-07-14 01:20:41,077 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:20:41,079 [INFO] Loaded cog: giveaway.py
2025-07-14 01:20:41,083 [INFO] Loaded cog: moderation.py
2025-07-14 01:20:41,084 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:20:41,085 [INFO] Loaded cog: rosters.py
2025-07-14 01:20:41,087 [INFO] Loaded cog: shipgame.py
2025-07-14 01:20:41,088 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:20:41,496 [INFO] UEX cache refreshed successfully
2025-07-14 01:20:41,497 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:20:41,498 [INFO] Loaded cog: voice.py
2025-07-14 01:20:41,501 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:20:41,502 [INFO] Loaded cog: welcome.py
2025-07-14 01:20:41,502 [INFO] logging in using static token
2025-07-14 01:20:42,303 [INFO] Shard ID None has connected to Gateway (Session ID: 290cf2dc4885993b1cfa79d03e248dee).
2025-07-14 01:20:44,307 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:20:44,508 [INFO] Synced 29 slash commands globally.
2025-07-14 01:20:44,509 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:20:49,211 [ERROR] Ignoring exception in command 'traderoutes'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 171, in traderoutes
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoutes' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 01:21:22,924 [ERROR] Error selecting vehicle: 'Message' object has no attribute 'edit_original_response'
2025-07-14 01:21:22,924 [ERROR] Ignoring exception in modal <VehicleSelectionModal timeout=None children=1>:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 1098, in on_submit
    # Get filtered routes with vehicle SCU consideration
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Message' object has no attribute 'edit_original_response'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\ui\modal.py", line 188, in _scheduled_task
    await self.on_submit(interaction)
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 1110, in on_submit
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 774, in send_message
    raise InteractionResponded(self._parent)
discord.errors.InteractionResponded: This interaction has already been responded to before
2025-07-14 01:23:19,177 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:23:19,179 [INFO] Loaded cog: embedgen.py
2025-07-14 01:23:19,180 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:23:19,182 [INFO] Loaded cog: giveaway.py
2025-07-14 01:23:19,185 [INFO] Loaded cog: moderation.py
2025-07-14 01:23:19,186 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:23:19,188 [INFO] Loaded cog: rosters.py
2025-07-14 01:23:19,189 [INFO] Loaded cog: shipgame.py
2025-07-14 01:23:19,191 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:23:19,739 [INFO] UEX cache refreshed successfully
2025-07-14 01:23:19,739 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:23:19,741 [INFO] Loaded cog: voice.py
2025-07-14 01:23:19,744 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:23:19,745 [INFO] Loaded cog: welcome.py
2025-07-14 01:23:19,745 [INFO] logging in using static token
2025-07-14 01:23:20,455 [INFO] Shard ID None has connected to Gateway (Session ID: c0a141fa36961b095c30a176d5d68d78).
2025-07-14 01:23:22,470 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:23:22,752 [INFO] Synced 29 slash commands globally.
2025-07-14 01:23:22,753 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:24:13,905 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:24:13,907 [INFO] Loaded cog: embedgen.py
2025-07-14 01:24:13,908 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:24:13,910 [INFO] Loaded cog: giveaway.py
2025-07-14 01:24:13,913 [INFO] Loaded cog: moderation.py
2025-07-14 01:24:13,915 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:24:13,916 [INFO] Loaded cog: rosters.py
2025-07-14 01:24:13,917 [INFO] Loaded cog: shipgame.py
2025-07-14 01:24:13,918 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:24:14,327 [INFO] UEX cache refreshed successfully
2025-07-14 01:24:14,327 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:24:14,329 [INFO] Loaded cog: voice.py
2025-07-14 01:24:14,332 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:24:14,333 [INFO] Loaded cog: welcome.py
2025-07-14 01:24:14,333 [INFO] logging in using static token
2025-07-14 01:24:15,105 [INFO] Shard ID None has connected to Gateway (Session ID: eb2d19af9ea04a23a93c78a07c84f1f3).
2025-07-14 01:24:17,131 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:24:17,333 [INFO] Synced 29 slash commands globally.
2025-07-14 01:24:17,334 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:24:44,699 [ERROR] Ignoring exception in command 'traderoutes'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 827, in _do_call
    return await self._callback(self.binding, interaction, **params)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\AegesNoxBot\cogs\uex_trade.py", line 171, in traderoutes
    await interaction.response.defer()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\interactions.py", line 661, in defer
    await adapter.create_interaction_response(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\webhook\async_.py", line 219, in request
    raise NotFound(response, data)
discord.errors.NotFound: 404 Not Found (error code: 10062): Unknown interaction

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\tree.py", line 1248, in _call
    await command._invoke_with_namespace(interaction, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 853, in _invoke_with_namespace
    return await self._do_call(interaction, transformed_values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\discord\app_commands\commands.py", line 846, in _do_call
    raise CommandInvokeError(self, e) from e
discord.app_commands.errors.CommandInvokeError: Command 'traderoutes' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-14 01:24:45,181 [ERROR] Error in traderoutes command: unsupported format string passed to NoneType.__format__
2025-07-14 01:26:54,566 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:26:54,567 [INFO] Loaded cog: embedgen.py
2025-07-14 01:26:54,568 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:26:54,570 [INFO] Loaded cog: giveaway.py
2025-07-14 01:26:54,573 [INFO] Loaded cog: moderation.py
2025-07-14 01:26:54,574 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:26:54,576 [INFO] Loaded cog: rosters.py
2025-07-14 01:26:54,578 [INFO] Loaded cog: shipgame.py
2025-07-14 01:26:54,579 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:26:55,024 [INFO] UEX cache refreshed successfully
2025-07-14 01:26:55,025 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:26:55,027 [INFO] Loaded cog: voice.py
2025-07-14 01:26:55,029 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:26:55,030 [INFO] Loaded cog: welcome.py
2025-07-14 01:26:55,030 [INFO] logging in using static token
2025-07-14 01:26:55,810 [INFO] Shard ID None has connected to Gateway (Session ID: 0eedd11daaa27e9b2be49072be4bc9d1).
2025-07-14 01:26:57,821 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:26:58,089 [INFO] Synced 29 slash commands globally.
2025-07-14 01:26:58,089 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:27:56,816 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:27:56,817 [INFO] Loaded cog: embedgen.py
2025-07-14 01:27:56,819 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:27:56,821 [INFO] Loaded cog: giveaway.py
2025-07-14 01:27:56,824 [INFO] Loaded cog: moderation.py
2025-07-14 01:27:56,825 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:27:56,827 [INFO] Loaded cog: rosters.py
2025-07-14 01:27:56,828 [INFO] Loaded cog: shipgame.py
2025-07-14 01:27:56,829 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:27:57,249 [INFO] UEX cache refreshed successfully
2025-07-14 01:27:57,250 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:27:57,251 [INFO] Loaded cog: voice.py
2025-07-14 01:27:57,254 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:27:57,255 [INFO] Loaded cog: welcome.py
2025-07-14 01:27:57,255 [INFO] logging in using static token
2025-07-14 01:27:57,896 [INFO] Shard ID None has connected to Gateway (Session ID: ea06699509eacf6f2966e8c63a2d5562).
2025-07-14 01:27:59,936 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:28:00,207 [INFO] Synced 29 slash commands globally.
2025-07-14 01:28:00,207 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:30:00,136 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:30:00,137 [INFO] Loaded cog: embedgen.py
2025-07-14 01:30:00,139 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:30:00,141 [INFO] Loaded cog: giveaway.py
2025-07-14 01:30:00,144 [INFO] Loaded cog: moderation.py
2025-07-14 01:30:00,145 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:30:00,147 [INFO] Loaded cog: rosters.py
2025-07-14 01:30:00,148 [INFO] Loaded cog: shipgame.py
2025-07-14 01:30:00,149 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:30:00,597 [INFO] UEX cache refreshed successfully
2025-07-14 01:30:00,598 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:30:00,599 [INFO] Loaded cog: voice.py
2025-07-14 01:30:00,603 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:30:00,603 [INFO] Loaded cog: welcome.py
2025-07-14 01:30:00,604 [INFO] logging in using static token
2025-07-14 01:30:01,623 [INFO] Shard ID None has connected to Gateway (Session ID: 8e5f84b7f3e03c8190db71fef6648c1e).
2025-07-14 01:30:03,623 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:30:03,879 [INFO] Synced 29 slash commands globally.
2025-07-14 01:30:03,880 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:30:12,573 [WARNING] Interaction expired while processing traderoutes command
2025-07-14 01:30:32,734 [ERROR] Error selecting vehicle: 'Message' object has no attribute 'edit_original_response'
2025-07-14 01:34:15,026 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:34:15,028 [INFO] Loaded cog: embedgen.py
2025-07-14 01:34:15,029 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:34:15,031 [INFO] Loaded cog: giveaway.py
2025-07-14 01:34:15,035 [INFO] Loaded cog: moderation.py
2025-07-14 01:34:15,036 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:34:15,037 [INFO] Loaded cog: rosters.py
2025-07-14 01:34:15,038 [INFO] Loaded cog: shipgame.py
2025-07-14 01:34:15,039 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:34:15,480 [INFO] UEX cache refreshed successfully
2025-07-14 01:34:15,480 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:34:15,482 [INFO] Loaded cog: voice.py
2025-07-14 01:34:15,485 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:34:15,486 [INFO] Loaded cog: welcome.py
2025-07-14 01:34:15,487 [INFO] logging in using static token
2025-07-14 01:34:16,236 [INFO] Shard ID None has connected to Gateway (Session ID: acf192732a4bcc83663a321a2d5a191e).
2025-07-14 01:34:18,258 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:34:18,667 [INFO] Synced 29 slash commands globally.
2025-07-14 01:34:18,667 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
2025-07-14 01:35:16,714 [INFO] Loaded cog: applytoaegis.py
2025-07-14 01:35:16,715 [INFO] Loaded cog: embedgen.py
2025-07-14 01:35:16,717 [INFO] Loaded cog: fleetdata.py
2025-07-14 01:35:16,719 [INFO] Loaded cog: giveaway.py
2025-07-14 01:35:16,723 [INFO] Loaded cog: moderation.py
2025-07-14 01:35:16,724 [INFO] Loaded cog: rolerequest.py
2025-07-14 01:35:16,725 [INFO] Loaded cog: rosters.py
2025-07-14 01:35:16,726 [INFO] Loaded cog: shipgame.py
2025-07-14 01:35:16,727 [INFO] Loaded cog: storagetracker.py
2025-07-14 01:35:17,174 [INFO] UEX cache refreshed successfully
2025-07-14 01:35:17,174 [INFO] Loaded cog: uex_trade.py
2025-07-14 01:35:17,176 [INFO] Loaded cog: voice.py
2025-07-14 01:35:17,179 [INFO] Loaded cog: voicetracker.py
2025-07-14 01:35:17,179 [INFO] Loaded cog: welcome.py
2025-07-14 01:35:17,179 [INFO] logging in using static token
2025-07-14 01:35:17,912 [INFO] Shard ID None has connected to Gateway (Session ID: 30c0781becca357ea52ed5ddaa16d5b6).
2025-07-14 01:35:19,946 [INFO] Logged in as Aegis Nox Bot
2025-07-14 01:35:20,314 [INFO] Synced 29 slash commands globally.
2025-07-14 01:35:20,314 [INFO] Registered commands: ['embedcreate', 'embededit', 'giveaway', 'giveawayend', 'giveawaylist', 'ban', 'kick', 'timeout', 'untimeout', 'warn', 'warnings', 'clearwarnings', 'clear', 'createroster', 'editroster', 'promote', 'demote', 'showdiscordfleet', 'discordfleetleaderboard', 'storage', 'traderoutes', 'commodities', 'price', 'setupvoice', 'voicetime', 'voicelb', 'msgcount', 'msglb', 'populatetracking']
