import asyncio
import os
from dotenv import load_dotenv
import sys
sys.path.append('.')
from cogs.uex_trade import UEXAPIClient

async def test_vehicles():
    load_dotenv()
    client = UEXAPIClient(os.getenv('UEX_API_TOKEN'))
    try:
        print("Testing UEX vehicles API...")
        vehicles = await client.get_vehicles()
        print(f'Successfully fetched {len(vehicles)} vehicles')
        
        # Show first few vehicles to see available fields
        print("\nFirst 3 vehicles and their fields:")
        for i, vehicle in enumerate(vehicles[:3], 1):
            print(f"{i}. {vehicle}")
            print()

        # Check what fields are available
        if vehicles:
            print("Available fields in vehicle data:")
            print(list(vehicles[0].keys()))
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_vehicles())
